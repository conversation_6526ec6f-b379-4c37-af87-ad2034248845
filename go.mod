module omni_worker

go 1.23.2

toolchain go1.23.4

require (
	github.com/avast/retry-go v3.0.0+incompatible
	github.com/bytedance/mockey v1.2.14
	github.com/go-resty/resty/v2 v2.16.2
	github.com/gogf/gf/v2 v2.8.1
	github.com/google/uuid v1.6.0
	github.com/json-iterator/go v1.1.12
	github.com/samber/lo v1.51.0
	github.com/smartystreets/goconvey v1.8.1
	github.com/stretchr/testify v1.10.0
	gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg v1.7.8-0.20250908083639-6e1c7fea9956
	gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto v1.0.8-0.20250919055837-887f48c07995
	gitlab.ttyuyin.com/avengers/tyr v1.1.12
	go.uber.org/zap v1.27.0
)

require (
	github.com/BurntSushi/toml v1.5.0 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-spi v0.0.5 // indirect
	github.com/alibabacloud-go/cdn-20180510/v7 v7.0.0 // indirect
	github.com/alibabacloud-go/darabonba-openapi/v2 v2.0.11 // indirect
	github.com/alibabacloud-go/debug v1.0.1 // indirect
	github.com/alibabacloud-go/endpoint-util v1.1.0 // indirect
	github.com/alibabacloud-go/openapi-util v0.1.1 // indirect
	github.com/alibabacloud-go/tea v1.2.2 // indirect
	github.com/alibabacloud-go/tea-utils/v2 v2.0.6 // indirect
	github.com/alibabacloud-go/tea-xml v1.1.3 // indirect
	github.com/aliyun/alibaba-cloud-sdk-go v1.63.79 // indirect
	github.com/aliyun/alibabacloud-oss-go-sdk-v2 v1.1.3 // indirect
	github.com/aliyun/credentials-go v1.4.5 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/clbanning/mxj v1.8.5-0.20200714211355-ff02cfb8ea28 // indirect
	github.com/clbanning/mxj/v2 v2.7.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/emirpasic/gods v1.18.1 // indirect
	github.com/fatih/color v1.17.0 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/google/go-querystring v1.0.0 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/grokify/html-strip-tags-go v0.1.0 // indirect
	github.com/huaweicloud/huaweicloud-sdk-go-obs v3.24.9+incompatible // indirect
	github.com/huaweicloud/huaweicloud-sdk-go-v3 v0.1.126 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/klauspost/compress v1.17.11 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.15 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-httpheader v0.2.1 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/olekukonko/tablewriter v0.0.5 // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20220228012449-10b1cf09e00b // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.55.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	github.com/tencentyun/cos-go-sdk-v5 v0.7.59 // indirect
	github.com/tencentyun/qcloud-cos-sts-sdk v0.0.0-20241118064430-63a76784514f // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	gitlab.ttyuyin.com/tt-infra/tyr v1.0.6 // indirect
	go.mongodb.org/mongo-driver v1.12.0 // indirect
	go.opentelemetry.io/otel v1.31.0 // indirect
	go.opentelemetry.io/otel/metric v1.31.0 // indirect
	go.opentelemetry.io/otel/sdk v1.31.0 // indirect
	go.opentelemetry.io/otel/trace v1.31.0 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	golang.org/x/arch v0.12.0 // indirect
	golang.org/x/crypto v0.33.0 // indirect
	golang.org/x/net v0.35.0 // indirect
	golang.org/x/sync v0.12.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	golang.org/x/time v0.6.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241216192217-9240e9c98484 // indirect
	google.golang.org/protobuf v1.36.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/gorm v1.25.12 // indirect
)

require (
	github.com/prometheus/client_golang v1.20.5
	golang.org/x/sys v0.30.0 // indirect
	google.golang.org/grpc v1.69.2
)
