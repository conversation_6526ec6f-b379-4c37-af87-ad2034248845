package obs

import (
	"context"
	"fmt"
	"io"
	"omni_worker/pkg/config"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/oss_util"
)

var (
	obsClient *utils.ObjectStorage
	obsOnce   sync.Once
	vendorMap = map[string]utils.ObjecetStorageType{
		"huawei":  utils.StorageTypeObs,
		"ali":     utils.StorageTypeOss,
		"tencent": utils.StorageTypeCos,
	}
)

// InitOBSClient 初始化OBS客户端
func InitOBSClient() {
	obsOnce.Do(func() {
		cfg := config.GetConfig()
		vendor, exists := vendorMap[cfg.OBS.Vendor]
		if !exists {
			panic(fmt.Sprintf("unsupport vendor:%s", cfg.OBS.Vendor))
		}
		var err error
		obsClient, err = utils.NewObjectStorage(&utils.StorageParameter{
			AccessKey:  cfg.OBS.AccessKey,
			SecretKey:  cfg.OBS.SecretKey,
			EndPoint:   cfg.OBS.Endpoint,
			BucketName: cfg.OBS.Bucket,
			CdnName:    cfg.OBS.CDN,
			CosUrl:     cfg.OBS.CosUrl,
			OssArn:     "xxxxxx", // 生成临时Key 才有用，这里先填空
		}, vendor)
		if err != nil {
			panic(err)
		}
		g.Log().Info(context.Background(), "OBS client initialized successfully")
	})
}

// GetOBSClient 获取OBS客户端
func GetOBSClient() *utils.ObjectStorage {
	return obsClient
}

// UploadFile 上传文件到OBS
func UploadFile(localFilePath, objectKey string) (string, error) {
	if obsClient == nil {
		return "", fmt.Errorf("OBS client not initialized")
	}

	// 默认使用公共读权限
	url, err := obsClient.UploadFile(localFilePath, objectKey, acl.AclTypePublicRead)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to OBS: %w", err)
	}

	return url, nil
}

// UploadFileWithACL 上传文件到OBS并指定ACL
func UploadFileWithACL(localFilePath, objectKey string, aclType acl.StorageAclType) (string, error) {
	if obsClient == nil {
		return "", fmt.Errorf("OBS client not initialized")
	}

	url, err := obsClient.UploadFile(localFilePath, objectKey, aclType)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to OBS: %w", err)
	}

	return url, nil
}

// UploadFileWithOptions 上传文件到OBS并指定选项
func UploadFileWithOptions(localFilePath, objectKey string, aclType acl.StorageAclType, options ...oss_util.ClientOption) (string, error) {
	if obsClient == nil {
		return "", fmt.Errorf("OBS client not initialized")
	}

	url, err := obsClient.UploadFile(localFilePath, objectKey, aclType, options...)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to OBS with options: %w", err)
	}

	return url, nil
}

// UploadBytes 上传字节流到OBS
func UploadBytes(r io.ReadCloser, objectKey string) (string, string, error) {
	if obsClient == nil {
		return "", "", fmt.Errorf("OBS client not initialized")
	}

	// 默认使用公共读权限
	url, etag, err := obsClient.UploadBytes(r, objectKey, acl.AclTypePublicRead)
	if err != nil {
		return "", "", fmt.Errorf("failed to upload bytes to OBS: %w", err)
	}

	return url, etag, nil
}

// DownloadFile 从OBS下载文件
func DownloadFile(objectKey, localFilePath string) error {
	if obsClient == nil {
		return fmt.Errorf("OBS client not initialized")
	}

	err := obsClient.DownloadFile(objectKey, localFilePath)
	if err != nil {
		return fmt.Errorf("failed to download file from OBS: %w", err)
	}

	return nil
}

// DownloadBytes 从OBS下载文件为字节流
func DownloadBytes(objectKey string) (io.ReadCloser, error) {
	if obsClient == nil {
		return nil, fmt.Errorf("OBS client not initialized")
	}

	reader, err := obsClient.DownloadBytes(objectKey)
	if err != nil {
		return nil, fmt.Errorf("failed to download bytes from OBS: %w", err)
	}

	return reader, nil
}

// SetPublicRead 设置对象为公共读权限
func SetPublicRead(objectKey string) (string, error) {
	if obsClient == nil {
		return "", fmt.Errorf("OBS client not initialized")
	}

	url, err := obsClient.SetPublicRead(objectKey)
	if err != nil {
		return "", fmt.Errorf("failed to set public read for object: %w", err)
	}

	return url, nil
}

// IsObjectExist 检查对象是否存在
func IsObjectExist(objectKey string) (bool, error) {
	if obsClient == nil {
		return false, fmt.Errorf("OBS client not initialized")
	}

	exists, err := obsClient.IsObjectExist(objectKey)
	if err != nil {
		return false, fmt.Errorf("failed to check if object exists: %w", err)
	}

	return exists, nil
}

// DeleteObject 删除对象
func DeleteObject(objectKey string) error {
	if obsClient == nil {
		return fmt.Errorf("OBS client not initialized")
	}

	err := obsClient.DeleteObject(objectKey)
	if err != nil {
		return fmt.Errorf("failed to delete object: %w", err)
	}

	return nil
}

// GetObjectUrl 获取对象URL
func GetObjectUrl(objectKey string) (string, string, error) {
	if obsClient == nil {
		return "", "", fmt.Errorf("OBS client not initialized")
	}

	url, cdnUrl, err := obsClient.GetObjectUrl(objectKey)
	if err != nil {
		return "", "", fmt.Errorf("failed to get object URL: %w", err)
	}

	return url, cdnUrl, nil
}

// Presign 生成预签名URL
func Presign(objectKey string, options ...oss_util.ClientOption) (string, error) {
	if obsClient == nil {
		return "", fmt.Errorf("OBS client not initialized")
	}

	url, err := obsClient.Presign(objectKey, options...)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	return url, nil
}
