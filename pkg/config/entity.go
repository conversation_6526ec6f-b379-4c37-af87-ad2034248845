package config

import (
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

var defaultWorkerCfg = &WorkerConfig{
	IntervalMilliseconds: 500,
	ReportSeconds:        5,
	MaxConcurrentTasks:   1,
}

// Config 结构体，包含所有配置字段
type Config struct {
	Server                        ServerConfig                  `json:"server" yaml:"server"`
	OmniEngine                    OmniEngineConfig              `json:"omni_engine" yaml:"omni_engine"`
	Logger                        LoggerConfig                  `json:"logger" yaml:"logger"`
	TTSService                    TTSServiceConfig              `json:"tts_service" yaml:"tts_service"`
	AudioSeparateService          AudioSeparateServiceConfig    `json:"audio_separate_service" yaml:"audio_separate_service"`
	VoiceIsolateService           VoiceIsolateServiceConfig     `json:"voice_isolate_service" yaml:"voice_isolate_service"`
	ASRService                    ASRServiceConfig              `json:"asr_service" yaml:"asr_service"`
	STSService                    STSServiceConfig              `json:"sts_service" yaml:"sts_service"`
	PVCTTSService                 PVCTTSServiceConfig           `json:"pvc_tts_service" yaml:"pvc_tts_service"`
	TextTranslateService          TextTranslateServiceConfig    `json:"text_translate_service" yaml:"text_translate_service"`
	AgentTranslateService         AgentTranslateServiceConfig   `json:"agent_translate_service" yaml:"agent_translate_service"`
	SRTTranslateService           SRTTranslateServiceConfig     `json:"srt_translate_service" yaml:"srt_translate_service"`
	VideoTranslateService         VideoTranslateConfig          `json:"video_translate_service" yaml:"video_translate_service"`
	OmniEngineGRPCConfig          *OmniEngineGRPCConfig         `json:"omni_engine_grpc_config" yaml:"omni_engine_grpc_config"`
	SubtitleMergeServiceConfig    SubtitleMergeServiceConfig    `json:"subtitle_merge_service_config" yaml:"subtitle_merge_service_config"`
	ThirdPartyTTSService          ThirdPartyTTSService          `json:"third_party_tts_service" yaml:"third_party_tts_service"`
	OBS                           OBSConfig                     `json:"obs" yaml:"obs"`
	ElevenLabsConfig              ElevenLabsConfig              `json:"eleven_labs_config" yaml:"eleven_labs_config"`
	AudioLangDetectServiceConfig  AudioLangDetectServiceConfig  `json:"audio_lang_detect_service_config" yaml:"audio_lang_detect_service_config"`
	VolcengineAsrConfigConfig     VolcengineAsrConfigConfig     `json:"volcengine_asr_config" yaml:"volcengine_asr_config"`
	VideoTranscodingConfig        VideoTranscodingConfig        `json:"video_transcoding_config" yaml:"video_transcoding_config"`
	CommentaryQiFeiConfig         CommentaryQiFeiConfig         `json:"commentary_qifei_config" yaml:"commentary_qifei_config"`
	VideoUnderstandingConfig      VideoUnderstandingConfig      `json:"video_understanding_config" yaml:"video_understanding_config"`
	VideoSubtitleGenerationConfig VideoSubtitleGenerationConfig `json:"video_subtitle_generation_config" yaml:"video_subtitle_generation_config"`
	VideoAlignmentConfig          VideoAlignmentConfig          `json:"video_alignment_config" yaml:"video_alignment_config"`
	VideoHighlightClippingConfig  VideoHighlightClippingConfig  `json:"video_highlight_clipping_config" yaml:"video_highlight_clipping_config"`
}
type ElevenLabsConfig struct {
	WorkerConfig
	ApiKey         string            `json:"api_key" yaml:"api_key"`
	CreateTTSUrl   string            `json:"create_tts_url" yaml:"create_tts_url"`
	CreateVoiceUrl string            `json:"create_voice_url" yaml:"create_voice_url"`
	DeleteVoiceUrl string            `json:"delete_voice_url" yaml:"delete_voice_url"`
	DefaultModelId string            `json:"default_model_id" yaml:"default_model_id"`
	ModelIdMap     map[string]string `json:"model_id_map" yaml:"model_id_map"`
}

func (c *ElevenLabsConfig) GetTTSModelId(lang string) string {
	if modelId, ok := c.ModelIdMap[lang]; ok {
		return modelId
	}
	return c.DefaultModelId
}

type ThirdPartyTTSService struct {
	DefaultWorkerConfig WorkerConfig                                `json:"default_worker_config" yaml:"default_worker_config"`
	TTSConfigMap        map[omniEngine.TaskType]ThirdPartyTTSConfig `json:"tts_config_map" yaml:"tts_config_map"`
}

// 在Config结构体的ThirdPartyTTSService中添加ModelExtra配置
type ThirdPartyTTSConfig struct {
	WorkerConfig WorkerConfig           `json:"worker_config" yaml:"worker_config"`
	Url          string                 `json:"url" yaml:"url"`
	AuthKey      string                 `json:"authKey" yaml:"authKey"`
	Model        string                 `json:"model" yaml:"model"`
	Format       string                 `json:"format" yaml:"format"`
	RetryConfig  RetryConfig            `json:"retry_config" yaml:"retry_config"`
	ModelExtra   map[string]interface{} `json:"modelExtra" yaml:"modelExtra"`
}
type RetryConfig struct {
	RetryInterval int     `json:"retry_interval" yaml:"retry_interval"`   // 重试间隔（秒）
	MaxRetryCount int     `json:"max_retry_count" yaml:"max_retry_count"` // 最大重试次数
	BaseDelay     int     `json:"base_delay" yaml:"base_delay"`           // 基础延迟（秒）
	MaxDelay      int     `json:"max_delay" yaml:"max_delay"`             // 最大延迟（秒）
	BackoffFactor float64 `json:"backoff_factor" yaml:"backoff_factor"`   // 退避因子
}

type OmniEngineGRPCConfig struct {
	DefaultCluster    string             `json:"default_cluster" yaml:"default_cluster"`
	EndPoints         map[string]string  `json:"endpoints" yaml:"endpoints"`
	TaskConsumeConfig *TaskConsumeConfig `json:"task_consume_config" yaml:"task_consume_config"`
}

// DeepEqual 深度比较两个配置是否相同
func (c *OmniEngineGRPCConfig) DeepEqual(other *OmniEngineGRPCConfig) bool {
	if c == nil || other == nil {
		return c == other
	}

	if c.DefaultCluster != other.DefaultCluster {
		return false
	}

	if len(c.EndPoints) != len(other.EndPoints) {
		return false
	}
	for k, v := range c.EndPoints {
		if otherV, ok := other.EndPoints[k]; !ok || v != otherV {
			return false
		}
	}

	return c.TaskConsumeConfig.DeepEqual(other.TaskConsumeConfig)
}

// DeepCopy 创建深层拷贝
func (c *OmniEngineGRPCConfig) DeepCopy() *OmniEngineGRPCConfig {
	if c == nil {
		return nil
	}

	copyConfig := &OmniEngineGRPCConfig{
		DefaultCluster: c.DefaultCluster,
	}

	if c.EndPoints != nil {
		copyConfig.EndPoints = make(map[string]string, len(c.EndPoints))
		for k, v := range c.EndPoints {
			copyConfig.EndPoints[k] = v
		}
	}

	copyConfig.TaskConsumeConfig = c.TaskConsumeConfig.DeepCopy()
	return copyConfig
}

type TaskConsumeConfig struct {
	Enable              bool               `json:"enable" yaml:"enable"`
	TaskConsumeRuleList []*TaskConsumeRule `json:"task_consume_rule" yaml:"task_consume_rule"`
}

// DeepEqual 方法实现
func (c *TaskConsumeConfig) DeepEqual(other *TaskConsumeConfig) bool {
	if c == nil || other == nil {
		return c == other
	}

	if c.Enable != other.Enable || len(c.TaskConsumeRuleList) != len(other.TaskConsumeRuleList) {
		return false
	}

	for i, rule := range c.TaskConsumeRuleList {
		if !rule.DeepEqual(other.TaskConsumeRuleList[i]) {
			return false
		}
	}
	return true
}

// DeepCopy 方法实现
func (c *TaskConsumeConfig) DeepCopy() *TaskConsumeConfig {
	if c == nil {
		return nil
	}

	copyConfig := &TaskConsumeConfig{
		Enable: c.Enable,
	}

	if c.TaskConsumeRuleList != nil {
		copyConfig.TaskConsumeRuleList = make([]*TaskConsumeRule, len(c.TaskConsumeRuleList))
		for i, rule := range c.TaskConsumeRuleList {
			copyConfig.TaskConsumeRuleList[i] = rule.DeepCopy()
		}
	}
	return copyConfig
}

type TaskConsumeRule struct {
	Cluster string `json:"cluster" yaml:"cluster"`
	Count   int    `json:"count" yaml:"count"`
}

// DeepEqual 方法实现
func (r *TaskConsumeRule) DeepEqual(other *TaskConsumeRule) bool {
	if r == nil || other == nil {
		return r == other
	}
	return r.Cluster == other.Cluster && r.Count == other.Count
}

// DeepCopy 方法实现
func (r *TaskConsumeRule) DeepCopy() *TaskConsumeRule {
	if r == nil {
		return nil
	}
	return &TaskConsumeRule{
		Cluster: r.Cluster,
		Count:   r.Count,
	}
}

type HealthCheckConfig struct {
	HealthCheckInterval int              `json:"health_check_interval" yaml:"health_check_interval"`
	TaskHealthUrlMap    map[int32]string `json:"task_health_url_map" yaml:"task_health_url_map"`
}

type ServerConfig struct {
	HTTPAddress string `json:"http_address" yaml:"http_address"`
	Env         string `json:"env" yaml:"env"`
}

type OmniEngineConfig struct {
	GRPCAddress string `json:"grpc_address" yaml:"grpc_address"`
}

type LoggerConfig struct {
	Level  string `json:"level" yaml:"level"`
	Stdout bool   `json:"stdout" yaml:"stdout"`
	Path   string `json:"path" yaml:"path"`
	File   string `json:"file" yaml:"file"`
}

type WorkerConfig struct {
	IntervalMilliseconds  int `json:"interval_milliseconds" yaml:"interval_milliseconds"`                           // 定时调度任务的时间间隔（毫秒）
	ReportSeconds         int `json:"report_seconds" yaml:"report_seconds"`                                         // 定时上报任务的时间间隔（秒）
	MaxConcurrentTasks    int `json:"max_concurrent_tasks" yaml:"max_concurrent_tasks"`                             // 最大同时处理的任务数
	TimeoutSeconds        int `json:"timeout_seconds" yaml:"timeout_seconds"`                                       // 任务超时时间（秒）
	SubmitTaskIntervalMs  int `json:"submit_task_interval_milliseconds" yaml:"submit_task_interval_milliseconds"`   // 提交任务的时间间隔（毫秒）
	CheckResultIntervalMs int `json:"check_result_interval_milliseconds" yaml:"check_result_interval_milliseconds"` // 检查结果的时间间隔（毫秒）
}

type TTSServiceConfig struct {
	WorkerConfig
	CheckWatermarkUrl      string `json:"check_watermark_url" yaml:"check_watermark_url"`
	CreateTTSUrl           string `json:"create_tts_url" yaml:"create_tts_url"`
	CheckStatusUrl         string `json:"check_status_url" yaml:"check_status_url"`
	MaxRetryCount          int    `json:"max_retry_count" yaml:"max_retry_count"`
	PollingTimeOutSeconds  int    `json:"polling_time_out_seconds" yaml:"polling_time_out_seconds"`
	PollingIntervalSeconds int    `json:"polling_interval_seconds" yaml:"polling_interval_seconds"`
}

type AudioSeparateServiceConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
	ResultUrl string `json:"result_url" yaml:"result_url"`
}

type VoiceIsolateServiceConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
	ResultUrl string `json:"result_url" yaml:"result_url"`
}

type ASRServiceConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
}

type STSServiceConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
	ResultUrl string `json:"result_url" yaml:"result_url"`
}

type PVCTTSServiceConfig struct {
	WorkerConfig
	CreateUrl              string `json:"create_url" yaml:"create_url"`
	ResultUrl              string `json:"result_url" yaml:"result_url"`
	MaxRetryCount          int    `json:"max_retry_count" yaml:"max_retry_count"`
	PollingTimeOutSeconds  int    `json:"polling_time_out_seconds" yaml:"polling_time_out_seconds"`
	PollingIntervalSeconds int    `json:"polling_interval_seconds" yaml:"polling_interval_seconds"`
}

type VideoTranslateConfig struct {
	WorkerConfig
	ExtractV2CreateUrl       string `json:"extract_v2_create_url" yaml:"extract_v2_create_url"`
	ExtractV2QueryUrl        string `json:"extract_v2_query_url" yaml:"extract_v2_query_url"`
	EraseV2CreateUrl         string `json:"erase_v2_create_url" yaml:"erase_v2_create_url"`
	EraseV2QueryUrl          string `json:"erase_v2_query_url" yaml:"erase_v2_query_url"`
	MergeV2CreateUrl         string `json:"merge_v2_create_url" yaml:"merge_v2_create_url"`
	MergeV2QueryUrl          string `json:"merge_v2_query_url" yaml:"merge_v2_query_url"`
	AudioPreprocessCreateUrl string `json:"audio_preprocess_create_url" yaml:"audio_preprocess_create_url"`
	PreprocessV2CreateUrl    string `json:"preprocess_v2_create_url" yaml:"preprocess_v2_create_url"`
	PreprocessV2QueryUrl     string `json:"preprocess_v2_query_url" yaml:"preprocess_v2_query_url"`
	ASRCorrectionCreateUrl   string `json:"asr_correction_create_url" yaml:"asr_correction_create_url"`
	TTSCorrectionCreateUrl   string `json:"tts_correction_create_url" yaml:"tts_correction_create_url"`
}

type SubtitleMergeServiceConfig struct {
	WorkerConfig
	MergeThreadCore int `json:"merge_thread_core" yaml:"merge_thread_core"`
}

type SRTTranslateServiceConfig struct {
	WorkerConfig
	SrtTranslateUrl string `yaml:"srt_translate_url" json:"srt_translate_url,omitempty"`
}

type TextTranslateServiceConfig struct {
	WorkerConfig
	CreateUrl string `yaml:"create_url" json:"create_url,omitempty"`
	ResultUrl string `yaml:"result_url" json:"result_url,omitempty"`
}

type AgentTranslateServiceConfig struct {
	WorkerConfig
	CreateUrl string `yaml:"create_url" json:"create_url,omitempty"`
	ResultUrl string `yaml:"result_url" json:"result_url,omitempty"`
}

// OBSConfig OBS配置
type OBSConfig struct {
	Vendor    string `yaml:"vendor" json:"vendor,omitempty"`
	AccessKey string `yaml:"access_key" json:"access_key,omitempty"`
	SecretKey string `yaml:"secret_key" json:"secret_key,omitempty"`
	Bucket    string `yaml:"bucket" json:"bucket,omitempty"`
	Endpoint  string `yaml:"endpoint" json:"endpoint,omitempty"`
	CDN       string `yaml:"cdn" json:"cdn,omitempty"`
	CosUrl    string `yaml:"cos_url" json:"cos_url,omitempty"`
	ObjectDir string `yaml:"object_dir" json:"object_dir,omitempty"`
}

type AudioLangDetectServiceConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
}
type VolcengineAsrConfigConfig struct {
	WorkerConfig
	SubmitUrl  string `json:"submit_url" yaml:"submit_url"`   // 提交ASR任务的URL
	ResultUrl  string `json:"result_url" yaml:"result_url"`   // 获取ASR任务结果的URL
	ApiKey     string `json:"api_key" yaml:"api_key"`         // API密钥
	AccessKey  string `json:"access_key" yaml:"access_key"`   // 访问密钥
	ResourceId string `json:"resource_id" yaml:"resource_id"` // 资源ID
}

type VideoTranscodingConfig struct {
	WorkerConfig
}

type CommentaryQiFeiConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
	AccessKey string `json:"access_key" yaml:"access_key"`
}

type VideoUnderstandingConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
}

type VideoSubtitleGenerationConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
}

type VideoAlignmentConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
}

type VideoHighlightClippingConfig struct {
	WorkerConfig
	CreateUrl string `json:"create_url" yaml:"create_url"`
}
