//
// Copyright (c) 2024 TT, Ltd. All Rights Reserved.
//

package config

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	"omni_worker/internal/consts"

	jsoniter "github.com/json-iterator/go"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
)

var (
	mu        sync.Mutex
	callbacks []func()
)

// 配置更新时回调
func (c *Config) Format() error {
	cfgStr, _ := jsoniter.MarshalToString(c)
	g.Log().Infof(context.Background(), "config updated: %s", cfgStr)

	mu.Lock()
	defer mu.Unlock()
	for _, callback := range callbacks {
		concurrent.GoSafe(func() {
			callback()
		})
	}
	return nil
}

var defaultConfigLoader *atomic.Value

// 初始化配置，失败直接panic
func InitConfig(ctx context.Context, filePath string) *Config {
	checkTaskType()
	initConfig(ctx, filePath)
	cfg := GetConfig()
	if cfg == nil {
		panic("cfg is nil")
	}
	cfgStr, _ := jsoniter.MarshalToString(cfg)
	g.Log().Infof(ctx, "InitConfig cfg:%s", cfgStr)
	return cfg
}

func initConfig(ctx context.Context, filePath string) {
	cfg := &Config{}
	// 加载配置文件
	g.Log().Infof(ctx, "InitConfig filePath:%s", filePath)
	atomCfg, err := ttconfig.AtomLoad(filePath, cfg)
	if nil != err {
		panic(fmt.Sprintf("InitConfig ttconfig.AtomLoad failed, err:%v", err))
	}
	defaultConfigLoader = atomCfg
}

// 获取配置信息
func GetConfig() *Config {
	return defaultConfigLoader.Load().(*Config)
}

// 注册一个回调，当配置更新时执行
func RegisterConfigChangeCallback(callback func()) {
	mu.Lock()
	defer mu.Unlock()
	callbacks = append(callbacks, callback)
}

func (c *Config) GetWorkerCfg(taskType consts.TaskTypeEnum) *WorkerConfig {
	switch taskType {
	case consts.TaskTypeEnumTTSMultiLang, consts.TaskTypeEnumTTSCanton, consts.TaskTypeEnumTTSVi, consts.TaskTypeEnumTTSPvc:
		return &c.TTSService.WorkerConfig
	case consts.TaskTypeEnumAudioSeparate, consts.TaskTypeEnumAudioLangDetect: // 这两同一个算法服务
		return &c.AudioSeparateService.WorkerConfig
	case consts.TaskTypeEnumASR:
		return &c.ASRService.WorkerConfig
	case consts.TaskTypeEnumVoiceIsolate:
		return &c.VoiceIsolateService.WorkerConfig
	case consts.TaskTypeEnumSTS:
		return &c.STSService.WorkerConfig
	case consts.TaskTypeEnumMinimaxTTS:
		taskTypeMinimax := omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_MINIMAX
		if ttsCfg, ok := c.ThirdPartyTTSService.TTSConfigMap[taskTypeMinimax]; ok {
			return &ttsCfg.WorkerConfig
		} else {
			return &c.ThirdPartyTTSService.DefaultWorkerConfig
		}
	case consts.TaskTypeEnum11LabsTTS:
		return &c.ElevenLabsConfig.WorkerConfig
	case consts.TaskTypeEnumAudioVolcengineAsr:
		return &c.VolcengineAsrConfigConfig.WorkerConfig
	case consts.TaskTypeEnumAgentTranslate:
		return &c.AgentTranslateService.WorkerConfig
	case consts.TaskTypeEnumVideoTranscoding:
		return &c.VideoTranscodingConfig.WorkerConfig
	case consts.TaskTypeEnumVideoCommentaryQiFei:
		return &c.CommentaryQiFeiConfig.WorkerConfig
	default:
		return defaultWorkerCfg
	}
}

// 必须配置，否则会panic
var taskTypeNameMap = map[int32]string{
	0:   "未知类型",
	20:  "TTS多语言_20",
	23:  "TTS_PVC_23",
	24:  "TTS_11LABS_24",
	25:  "TTS_MINIMAX_IVC_25",
	27:  "TTS_MINIMAX_27",
	40:  "音频分离_40",
	50:  "ASR_50",
	60:  "音色转换_60",
	70:  "噪音分离_70",
	81:  "视频翻译擦除_81",
	82:  "视频翻译OCR提取_82",
	83:  "视频翻译预处理_83",
	84:  "视频翻译合成_84",
	86:  "ASR校正_86",
	87:  "TTS细分_87",
	101: "全文本翻译_101",
	102: "SRT文本翻译_102",
	103: "agent文本翻译_103",
	104: "视频转码_104",
	105: "起飞解说_105",
	106: "视频理解_106",
	107: "视频字幕生成_107",
	108: "视频音画对齐_108",
	109: "视频高光剪辑_109",
	181: "专业擦除_181",
	183: "专业预处理_183",
	190: "火山云ASR_190",
	200: "语种识别_200",
	210: "长字幕合成_210",
	211: "字幕合成_211",
	212: "解说字幕合成_212",
	213: "多模态识别_213",
}

func GetTaskNameByType(taskType omniEngine.TaskType) string {
	taskName, exists := taskTypeNameMap[int32(taskType)]
	if !exists {
		panic(fmt.Sprintf("taskType %d not exists", int32(taskType)))
	}
	return taskName
}

func checkTaskType() {
	// 对比taskTypeNameMap和proto中定义的任务类型是否一致
	for k := range omniEngine.TaskType_name {
		if _, ok := taskTypeNameMap[k]; !ok {
			panic(fmt.Sprintf("taskType %d not exists", k))
		}
	}
}

var healthCheckURLs = map[int32]string{
	20:  "http://127.0.0.1:6030/health",
	23:  "http://127.0.0.1:5000/health",
	40:  "http://127.0.0.1:6030/health",
	50:  "http://127.0.0.1:6030/health",
	60:  "http://127.0.0.1:6030/health",
	70:  "http://127.0.0.1:6033/health",
	81:  "http://127.0.0.1:35005/health",
	82:  "http://127.0.0.1:35000/health",
	83:  "http://127.0.0.1:35005/health",
	84:  "http://127.0.0.1:35005/health",
	86:  "http://127.0.0.1:35001/health",
	87:  "http://127.0.0.1:6030/health",
	101: "http://127.0.0.1:35001/health",
	102: "http://127.0.0.1:35001/health",
	103: "http://127.0.0.1:35001/health",
	181: "http://127.0.0.1:35005/health",
	183: "http://127.0.0.1:35005/health",
	200: "http://127.0.0.1:6030/health",
}

func GetHealthCheckURL(taskType omniEngine.TaskType) string {
	url, exists := healthCheckURLs[int32(taskType)]
	if !exists || len(url) == 0 {
		panic("health check url not exists")
	}
	return url
}
