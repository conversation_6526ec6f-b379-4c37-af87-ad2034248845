package media

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os/exec"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
)

// ffmpeg -i notitle.mp4 -i /translate_server/bin/watermark_h.png -threads 8 -c:v libx264 -crf 21 -preset ultrafast
// -filter_complex "[1:v]scale=iw*0.5:-1[wm];[0:v][wm]overlay=5:5,subtitles=merge.ass" -c:a copy -y ultrafast.mp4

var (
	FfmpegPath string

	hWaterFormat = WaterMarkInfo{
		PngPath:   "/data/translate_subtitle/watermark_h.png",
		PngWidth:  480,
		PngHeight: 128,
		// VideoWidth:   251,
		// VideoHeight:  446,
		MarginRight:  1,
		MarginBottom: 1,
	}

	wWaterFormat = WaterMarkInfo{
		PngPath:   "/data/translate_subtitle/watermark_w.png",
		PngWidth:  480,
		PngHeight: 128,
		// VideoWidth:   794,
		// VideoHeight:  446,
		MarginRight:  1,
		MarginBottom: 1,
	}
)

func MediaInit() error {
	// 1. 检测ffmpeg是否可用
	// 2. 加载水印图片
	if _, err := CheckFFmpegInstallation(); err != nil {
		return errors.New("ffmpeg is not installed or not found in PATH")
	}
	if err := InitWaterMarkImage(); err != nil {
		return err
	}
	return nil
}

func CheckFFmpegInstallation() (string, error) {
	cmd := exec.Command(FfmpegPath+"ffmpeg", "-version")
	var stdout bytes.Buffer
	cmd.Stdout = &stdout
	if err := cmd.Run(); err != nil {
		return "", errors.New("ffmpeg is not installed or not found in PATH")
	}
	versionInfo := stdout.String()
	return versionInfo, nil
}

func InitWaterMarkImage() error {
	avInfo, err := GetAudioVideoInfo(hWaterFormat.PngPath)
	if err != nil {
		return err
	}
	hWaterFormat.PngWidth = int(avInfo.ImageInfo.Width)
	hWaterFormat.PngHeight = int(avInfo.ImageInfo.Height)

	avInfo, err = GetAudioVideoInfo(wWaterFormat.PngPath)
	if err != nil {
		return err
	}
	wWaterFormat.PngWidth = int(avInfo.ImageInfo.Width)
	wWaterFormat.PngHeight = int(avInfo.ImageInfo.Height)
	return nil
}

func GetAudioVideoInfo(inputFile string) (avInfo *AudioVideoInfo, err error) {
	defer func() {
		if errP := recover(); errP != nil {
			fmt.Println("GetAudioVideoInfo, panic:", errP)
			avInfo = nil
			err = fmt.Errorf("GetAudioVideoInfo, panic")
		}
	}()
	// ffprobe -v error -print_format json -show_format -show_streams x.mp4
	cmd := exec.Command(FfmpegPath+"ffprobe", "-v", "error", "-print_format", "json", "-show_format", "-show_streams", inputFile)
	var out bytes.Buffer
	cmd.Stdout = &out
	if err := cmd.Run(); err != nil {
		fmt.Println("ffprobe error:", err)
		return nil, err
	} else {
		Str := strings.TrimSpace(out.String())

		info := &StreamsInfo{}
		err = json.Unmarshal([]byte(Str), info)
		if err != nil {
			return nil, err
		} else {
			// num := info.GetVideoFrameNum()
			// fmt.Println(num)
			avInfo := info.GetAudioVideoInfo()
			return avInfo, nil
		}
	}
}

func CombineVideoAndSubtitlesWithPreset2() error {
	// ffmpeg -i notitle.mp4 -i /translate_server/bin/watermark_h.png -threads 8 -c:v libx264 -crf 21 -preset ultrafast
	// -filter_complex "[1:v]scale=iw*0.5:-1[wm];[0:v][wm]overlay=5:5,subtitles=merge.ass" -c:a copy -y ultrafast.mp4
	return fmt.Errorf("CombineVideoAndSubtitlesWithPreset2 not implemented")
}

func CombineVideoAndSubtitlesWithPreset(taskId int64, videoFile, audioFile, subtitleFile, outputFullPath string, crf, preset string, threads int, withWatermark, vocalTask bool) error {
	// ffmpeg -i notitle.mp4 -i /translate_server/bin/watermark_h.png -threads 8 -c:v libx264 -crf 21 -preset ultrafast
	// -filter_complex "[1:v]scale=iw*0.5:-1[wm];[0:v][wm]overlay=5:5,subtitles=merge.ass" -c:a copy -y ultrafast.mp4

	threadCore := "4"
	if threads > 0 {
		threadCore = strconv.FormatInt(int64(threads), 10)
	}

	avInfo, err := GetAudioVideoInfo(videoFile)
	if err != nil {
		return fmt.Errorf("合成视频时获取媒体信息失败, err: %s", err.Error())
	}
	if avInfo == nil {
		return fmt.Errorf("avInfo is nil")
	}
	g.Log().Infof(context.Background(), "合成视频字幕 1, taskid: %v", taskId)
	var winfo *WaterMarkInfo
	var pngCoef float32 = 0.6
	g.Log().Infof(context.Background(), "合成视频字幕 1, taskid: %v, width: %d, height: %d", taskId, avInfo.VideoInfo.Width, avInfo.VideoInfo.Height)
	if avInfo.VideoInfo.Width > avInfo.VideoInfo.Height {
		winfo = &wWaterFormat
		pngCoef = 0.3
	} else {
		winfo = &hWaterFormat
	}
	g.Log().Infof(context.Background(), "合成视频字幕 2, taskid: %v", taskId)
	if winfo == nil {
		return fmt.Errorf("winfo is nil, width: %d, height: %d", avInfo.VideoInfo.Width, avInfo.VideoInfo.Height)
	}
	fc := ""
	if withWatermark {
		coef := (float32(avInfo.VideoInfo.Width) * pngCoef) / float32(winfo.PngWidth) // 根据水印要显示的宽度，计算缩放比例
		rightMargin := int(avInfo.VideoInfo.Width) - int(float32(winfo.MarginRight)*coef) - int(coef*float32(winfo.PngWidth))
		bottomMargin := int(avInfo.VideoInfo.Height) - int(float32(winfo.MarginBottom)*coef) - int(coef*float32(winfo.PngHeight))
		waterFormat := fmt.Sprintf("[1:v]scale=iw*%v:-1[wm];[0:v][wm]overlay=%v:%v", coef, rightMargin, bottomMargin)
		fc = fmt.Sprintf("%s,subtitles=%s", waterFormat, subtitleFile)
	} else {
		fc = fmt.Sprintf("subtitles=%s", subtitleFile)
	}

	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", videoFile)
	if withWatermark {
		cmd.Args = append(cmd.Args, "-i", winfo.PngPath)
	}
	if vocalTask && len(audioFile) > 3 {
		cmd.Args = append(cmd.Args, "-i", audioFile)
	}
	cmd.Args = append(cmd.Args,
		"-threads", threadCore,
		"-filter_complex", fc,
		"-c:v", "libx264",
		"-crf", crf,
		"-preset", preset)
	if vocalTask && len(audioFile) > 3 {
		cmd.Args = append(cmd.Args, "-c:a", "aac")
	} else {
		cmd.Args = append(cmd.Args, "-c:a", "copy")
	}
	cmd.Args = append(cmd.Args, "-y", outputFullPath)

	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Printf("worker合成视频字幕, taskid: %v, waterMark: %v, vocalTask: %v, audioFile: %v, cmdStr: %s",
		taskId, withWatermark, vocalTask, audioFile, cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to CombineVideoAndSubtitlesWithPreset, error: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

func MergeAudioAndVideoWithAac(audioInput, videoInput, videoOutput string) error {
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", audioInput,
		"-i", videoInput,
		"-c:v", "copy",
		"-c:a", "aac",
		"-y",
		videoOutput)
	//"-c:a", "libmp3lame",
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to merge audio and video: %v\noutput: %s", err, stdoutStderr)
		return errors.New(s)
	}
	return nil
}

// VideoTranscoding 视频转码
// videoFile 输入视频文件路径
// outputPath 输出视频文件路径
// resolution 输出视频分辨率
// crf 视频质量
// fps 输出视频帧率
// threads 线程数
func VideoTranscoding(ctx context.Context, videoFile, outputPath, resolution, preset string, crf, fps int) error {
	defaultFps := "30"
	if fps > 0 {
		defaultFps = strconv.FormatInt(int64(fps), 10)
	}
	defaultCrf := "28"
	if crf > 0 {
		defaultCrf = strconv.FormatInt(int64(crf), 10)
	}
	// 解析720x1080为720:1080
	resolution = strings.ReplaceAll(resolution, "x", ":")
	// 视频转码，假如视频没有音频流，添加静音音频
	// ffmpeg -i x.mp4 -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -vf "scale=480:854:force_original_aspect_ratio=decrease,pad=480:854:(ow-iw)/2:(oh-ih)/2" -c:v libx264 -crf 28 -r 30 -preset ultrafast -c:a aac -b:a 96k -shortest -y output.mp4
	cmd := exec.Command("ffmpeg",
		"-i", videoFile,
		"-f", "lavfi",
		"-i", "anullsrc=channel_layout=stereo:sample_rate=44100",
		"-vf", fmt.Sprintf("scale=%s:force_original_aspect_ratio=decrease,pad=%s:(ow-iw)/2:(oh-ih)/2,setsar=1", resolution, resolution),
		"-c:v", "libx264",
		"-r", defaultFps,
		"-crf", defaultCrf,
		"-preset", preset,
		"-c:a", "aac",
		"-b:a", "96k",
		"-shortest",
		"-loglevel", "error",
		"-y", outputPath)

	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(ctx, "[VideoTranscoding] cmd: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("videoTranscoding failed, error: %v, output: %s", err, stdoutStderr)
	}
	return nil
}

// 合并并归一化音频
func MergeVideosWithNormalization(ctx context.Context, filePaths []string, output string) error {
	args := []string{}
	for _, filePath := range filePaths {
		args = append(args, "-i", filePath)
	}
	filter := strings.Builder{}
	// 处理所有视频和音频流
	for i := 0; i < len(filePaths); i++ {
		filter.WriteString(fmt.Sprintf("[%d:v]setsar=1[v%d];", i, i))
		filter.WriteString(fmt.Sprintf("[%d:a]aformat=sample_fmts=fltp:channel_layouts=stereo:sample_rates=44100[a%d];", i, i))
	}

	// 连接所有处理后的流
	for i := 0; i < len(filePaths); i++ {
		filter.WriteString(fmt.Sprintf("[v%d][a%d]", i, i))
	}

	// 完成连接并添加音量标准化
	filter.WriteString(fmt.Sprintf("concat=n=%d:v=1:a=1[outv][outa];", len(filePaths)))
	filter.WriteString("[outa]loudnorm=I=-23:TP=-2:LRA=11[finala]")

	// 添加滤镜参数
	args = append(args, "-filter_complex", filter.String())

	// 添加输出参数
	args = append(args,
		"-map", "[outv]",
		"-map", "[finala]",
		"-c:v", "libx264",
		"-c:a", "aac",
		"-ar", "44100",
		"-max_muxing_queue_size", "9999",
		"-loglevel", "error",
		"-y", output)
	cmd := exec.Command("ffmpeg", args...)
	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(ctx, "[MergeVideosWithConcatDemuxer] cmd: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("merge video failed, error: %v, output: %s", err, stdoutStderr)
	}
	return nil
}

func MergeVideos(ctx context.Context, filePaths []string, output string) error {
	// 创建文件列表
	// ffmpeg -i input1.mp4 -i input2.mp4 -i input3.mp4 \
	// -filter_complex "[0:v]setsar=1[0:a][1:v]setsar=1[1:a][2:v]setsar=1[2:a]concat=n=3:v=1:a=1[outv][outa]" \
	// -map "[outv]" -map "[outa]" \
	// -c:v libx264 -c:a aac final_output.mp4
	args := []string{}
	for _, filePath := range filePaths {
		args = append(args, "-i", filePath)
	}
	filter := strings.Builder{}
	for i := 0; i < len(filePaths); i++ {
		filter.WriteString(fmt.Sprintf("[%d:v]setsar=1[v%d];", i, i))
	}
	for i := 0; i < len(filePaths); i++ {
		filter.WriteString(fmt.Sprintf("[v%d][%d:a]", i, i))
	}
	filter.WriteString(fmt.Sprintf("concat=n=%d:v=1:a=1[outv][outa]", len(filePaths)))
	args = append(args, "-filter_complex", filter.String())
	args = append(args,
		"-map", "[outv]",
		"-map", "[outa]",
		"-c:v", "libx264",
		"-c:a", "aac",
		"-loglevel", "error",
		"-y", output)
	cmd := exec.Command("ffmpeg", args...)
	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(ctx, "[MergeVideosWithConcatDemuxer] cmd: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("merge video failed, error: %v, output: %s", err, stdoutStderr)
	}
	return nil
}

// MergeAndTranscodeVideos 合并并转码视频（一步完成）
func MergeAndTranscodeVideos(ctx context.Context, filePaths []string, output, resolution, preset string, crf, fps int) error {
	defaultFps := "30"
	if fps > 0 {
		defaultFps = strconv.FormatInt(int64(fps), 10)
	}
	defaultCrf := "28"
	if crf > 0 {
		defaultCrf = strconv.FormatInt(int64(crf), 10)
	}
	// 解析720x1080为720:1080
	resolution = strings.ReplaceAll(resolution, "x", ":")
	
	args := []string{}
	for _, filePath := range filePaths {
		args = append(args, "-i", filePath)
	}
	
	// 构建滤镜器字符串
	filter := strings.Builder{}
	// 处理所有视频流，统一缩放和设置宽高比
	for i := 0; i < len(filePaths); i++ {
		filter.WriteString(fmt.Sprintf("[%d:v]scale=%s:force_original_aspect_ratio=decrease,pad=%s:(ow-iw)/2:(oh-ih)/2,setsar=1[v%d];", i, resolution, resolution, i))
	}
	// 处理所有音频流
	for i := 0; i < len(filePaths); i++ {
		filter.WriteString(fmt.Sprintf("[%d:a]aformat=sample_fmts=fltp:channel_layouts=stereo:sample_rates=44100[a%d];", i, i))
	}
	
	// 连接所有处理后的流
	for i := 0; i < len(filePaths); i++ {
		filter.WriteString(fmt.Sprintf("[v%d][a%d]", i, i))
	}
	
	// 完成连接
	filter.WriteString(fmt.Sprintf("concat=n=%d:v=1:a=1[outv][outa]", len(filePaths)))
	
	// 添加滤镜参数
	args = append(args, "-filter_complex", filter.String())
	
	// 添加输出参数
	args = append(args,
		"-map", "[outv]",
		"-map", "[outa]",
		"-c:v", "libx264",
		"-r", defaultFps,
		"-crf", defaultCrf,
		"-preset", preset,
		"-c:a", "aac",
		"-b:a", "96k",
		"-max_muxing_queue_size", "9999",
		"-loglevel", "error",
		"-y", output)
	
	cmd := exec.Command("ffmpeg", args...)
	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(ctx, "[MergeAndTranscodeVideos] cmd: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("merge and transcode video failed, error: %v, output: %s", err, stdoutStderr)
	}
	return nil
}

// NormalizeAudio 音量归一化
func NormalizeAudio(ctx context.Context, inputPath, outputPath string) error {
	cmd := exec.Command("ffmpeg",
		"-i", inputPath,
		"-af", "loudnorm=I=-23:TP=-2:LRA=11",
		"-c:v", "copy",
		"-c:a", "aac",
		"-loglevel", "error",
		"-y", outputPath)
	
	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(ctx, "[NormalizeAudio] cmd: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("audio normalization failed, error: %v, output: %s", err, stdoutStderr)
	}
	return nil
}

func VideoValidate(ctx context.Context, videoPath string) error {
	mediaInfo, err := GetAudioVideoInfo(videoPath)
	if err != nil {
		return fmt.Errorf("video validate failed, error: %v", err)
	}
	if mediaInfo == nil || mediaInfo.VideoInfo.Duration == -1 {
		return fmt.Errorf("video validate failed, video info is nil")
	}
	return nil
}
