package media

import (
	"context"
	"fmt"
	"io"
	"math"
	"os"
	"os/exec"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
)

var videoCodecs = []string{"h264", "hevc", "avc", "h265", "av1", "vp9", "vp8", "mpeg", "mpeg4", "mpeg-4", "prores"}

func isVideoCodec(codecName string) bool {
	codecName = strings.ToLower(codecName)
	for _, val := range videoCodecs {
		if strings.Contains(codecName, val) {
			return true
		}
	}
	return false
}

func isImageCodec(codecName string) bool {
	codecName = strings.ToLower(codecName)
	if strings.Contains(codecName, "png") || strings.Contains(codecName, "jpg") || strings.Contains(codecName, "jpeg") {
		return true
	}
	return false
}

type MediaStreamInfo struct {
	Index              int    `json:"index"`
	CodecName          string `json:"codec_name"`
	CodecType          string `json:"codec_type"`
	AvgFrameRate       string `json:"avg_frame_rate"`
	BitRate            string `json:"bit_rate"`
	SampleAspectRatio  string `json:"sample_aspect_ratio"`
	DisplayAspectRatio string `json:"display_aspect_ratio"`
	Width              int32  `json:"width"`
	Height             int32  `json:"height"`
	Duration           string `json:"duration"`
	SampleRate         string `json:"sample_rate"`
	Channels           int    `json:"channels"`
	TimeBase           string `json:"time_base"`
	CodecTagString     string `json:"codec_tag_string"`
}

type StreamsInfo struct {
	Streams []*MediaStreamInfo `json:"streams"`
}

type WaterMarkInfo struct {
	PngPath      string // 水印图片路径
	PngWidth     int    // 水印的宽高
	PngHeight    int
	VideoWidth   int // 对应视频的宽高
	VideoHeight  int
	MarginRight  int // 右边距
	MarginBottom int // 底部边距
}

type VideoInfo struct {
	AvgFrameRate       int // 帧率
	Width              int32
	Height             int32
	BitRate            int32
	Duration           int
	DurationFloat      float64
	CodecName          string
	SampleAspectRatio  string
	DisplayAspectRatio string
	TimeBase           string
}

type AudioInfo struct {
	BitRate       int
	Duration      int
	DurationFloat float64
	DurationStr   string
	SampleRate    int
	Channels      int
	CodecName     string
}

type ImageInfo struct {
	Width     int32
	Height    int32
	CodecName string
	Duration  int
}

type DataInfo struct {
	CodecTagString string
}

type AudioVideoInfo struct {
	VideoInfo
	AudioInfo
	DataInfo
	ImageInfo
}

func (s *StreamsInfo) GetAudioVideoInfo() *AudioVideoInfo {
	info := &AudioVideoInfo{}
	info.AudioInfo.Duration = -1
	info.VideoInfo.Duration = -1
	info.ImageInfo.Duration = -1
	for _, val := range s.Streams {
		if val.CodecType == "video" {
			if isImageCodec(val.CodecName) {
				info.ImageInfo.CodecName = val.CodecName
				info.ImageInfo.Width = val.Width
				info.ImageInfo.Height = val.Height
				continue
			}
			if !isVideoCodec(val.CodecName) {
				continue
			}
			idx := strings.Index(val.AvgFrameRate, "/")
			if idx >= 0 {
				numStr := val.AvgFrameRate[0:idx]
				denStr := val.AvgFrameRate[(idx + 1):]
				num, err := strconv.Atoi(numStr)
				den, err2 := strconv.Atoi(denStr)
				if err == nil && err2 == nil && den > 0 {
					info.VideoInfo.AvgFrameRate = num / den
				}
			}
			info.VideoInfo.CodecName = val.CodecName
			// 宽高
			info.VideoInfo.Width = val.Width
			info.VideoInfo.Height = val.Height
			info.SampleAspectRatio = val.SampleAspectRatio
			info.DisplayAspectRatio = val.DisplayAspectRatio
			// 码率
			bitRate, err := strconv.Atoi(val.BitRate)
			if err == nil {
				info.VideoInfo.BitRate = int32(bitRate)
			}
			// 时长（秒）
			duration, err := strconv.ParseFloat(val.Duration, 64)
			if err == nil {
				info.VideoInfo.Duration = int(duration)
				info.VideoInfo.DurationFloat = duration
			}
			info.VideoInfo.TimeBase = val.TimeBase
		}
		// 音频
		if val.CodecType == "audio" {
			// 码率
			bitRate, err := strconv.Atoi(val.BitRate)
			if err == nil {
				info.AudioInfo.BitRate = bitRate
			}
			// 时长（秒）
			duration, err := strconv.ParseFloat(val.Duration, 64)
			if err == nil {
				info.AudioInfo.Duration = int(duration)
				info.AudioInfo.DurationFloat = duration
				info.AudioInfo.DurationStr = val.Duration
			}
			info.AudioInfo.CodecName = val.CodecName
			// 通道数
			info.AudioInfo.Channels = val.Channels
			// 采样率
			sampleRate, err := strconv.Atoi(val.SampleRate)
			if err == nil {
				info.AudioInfo.SampleRate = sampleRate
			}
		}
		if val.CodecType == "data" {
			info.DataInfo.CodecTagString = val.CodecTagString
		}
	}

	return info
}

func (s *StreamsInfo) GetVideoFrameNum() int {
	for _, val := range s.Streams {
		if val.CodecType == "video" {
			idx := strings.Index(val.AvgFrameRate, "/")
			if idx >= 0 {
				numStr := val.AvgFrameRate[0:idx]
				num, err := strconv.Atoi(numStr)
				if err == nil {
					return num
				}
			}
			break
		}
	}

	return 0
}

func ProcessAudioDuration(ctx context.Context, audioPath string, minDuration, maxDuration float64) (string, error) {
	// 获取音频时长
	duration, err := GetAudioDuration(ctx, audioPath)
	if err != nil {
		return "", fmt.Errorf("failed to get audio duration: %v", err)
	}
	g.Log().Infof(ctx, "[ProcessAudioDuration] Processing audio duration: %f seconds", duration)

	// 添加最小时长保护，避免极短音频导致命令行参数过长
	if duration < 0.01 {
		return "", fmt.Errorf("audio too short: %f seconds, minimum 0.01s required", duration)
	}
	// 如果时长在10-300秒之间，直接返回
	if duration >= minDuration && duration <= maxDuration {
		return audioPath, nil
	}

	// 创建输出临时文件
	outputFile, err := os.CreateTemp("", "processed_*.mp3")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %v", err)
	}
	outputFile.Close()
	outputPath := outputFile.Name()
	defer os.Remove(outputPath)

	var args []string
	if duration < minDuration {
		// 计算需要重复的次数，确保至少11秒
		repeatCount := int(math.Ceil((minDuration + 1) / duration))
		if repeatCount > 1000 {
			return "", fmt.Errorf("audio too short, would require %d repetitions (max 1000)", repeatCount)
		}
		// 先创建一个包含重复音频的临时文件列表,然后使用concat demuxer进行拼接
		concatFile, err := os.CreateTemp("", "concat_*.txt")
		if err != nil {
			return "", fmt.Errorf("failed to create concat file: %v", err)
		}
		defer os.Remove(concatFile.Name())

		// 写入concat文件内容
		for i := 0; i < repeatCount; i++ {
			_, err = concatFile.WriteString(fmt.Sprintf("file '%s'\n", audioPath))
			if err != nil {
				concatFile.Close()
				return "", fmt.Errorf("failed to write concat file: %v", err)
			}
		}
		concatFile.Close()

		// 使用concat demuxer进行拼接
		args = []string{
			"-f", "concat",
			"-safe", "0",
			"-i", concatFile.Name(),
			"-c", "copy",
			"-y",
			outputPath,
		}
	} else {
		// 截取前299秒
		args = []string{
			"-i", audioPath,
			"-t", fmt.Sprintf("%.0f", maxDuration),
			"-acodec", "copy",
			"-y",
			outputPath,
		}
	}

	cmd := exec.CommandContext(ctx, "ffmpeg", args...)
	if err = cmd.Run(); err != nil {
		return "", fmt.Errorf("failed to process audio: %v", err)
	}

	// 直接移动文件而不是复制
	if err = os.Rename(outputPath, audioPath); err != nil {
		// 如果重命名失败，则复制文件
		return "", copyFile(outputPath, audioPath)
	}
	// 获取复制后的视频时长
	duration, err = GetAudioDuration(ctx, audioPath)
	if err != nil {
		return "", fmt.Errorf("failed to get audio duration: %v", err)
	}
	g.Log().Infof(ctx, "[GetAudioDuration]  processed audio duration: %f seconds", duration)
	return audioPath, nil
}

func GetAudioDuration(ctx context.Context, audioPath string) (float64, error) {
	args := []string{
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		audioPath,
	}
	cmd := exec.CommandContext(ctx, "ffprobe", args...)

	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("failed to get audio duration: %v", err)
	}

	duration, err := strconv.ParseFloat(strings.TrimSpace(string(output)), 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse duration: %v", err)
	}

	return duration, nil
}

func copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}
