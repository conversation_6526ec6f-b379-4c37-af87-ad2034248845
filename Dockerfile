# 使用Go 1.23.1的官方镜像作为构建环境
FROM golang:1.23.1-alpine as builder

# 设置工作目录
WORKDIR /build

# 复制项目所有内容到构建镜像
COPY . .

# 下载依赖项
RUN go env -w GOPROXY=http://yw-nexus.ttyuyin.com:8081/repository/group-go,direct &&\
    go env -w GO111MODULE=on &&\
    go mod tidy

# 构建可执行文件
RUN CGO_ENABLED=0 go build -o /build/omni-worker main.go
RUN chmod +x /build/omni-worker

FROM cr.ttyuyin.com/x-project/ubuntu:22-ffmpeg6

# 创建应用和配置文件的目录结构
RUN mkdir -p /app/bin && \
    mkdir -p /app/configs

# 设置时区
ENV TZ=Asia/Shanghai

# 从构建阶段复制可执行文件到运行环境
COPY --from=builder /build/omni-worker /app/bin/omni-worker

# 复制配置文件到容器
COPY configs/config.local.yaml /app/configs/config.local.yaml
COPY configs/config.test.yaml /app/configs/config.test.yaml
COPY configs/config.prod.yaml /app/configs/config.prod.yaml

# 设置构建时的环境变量
ARG CONFIG_ENV=test

# 运行时的环境变量
ENV CONFIG_ENV=$CONFIG_ENV

# 启动服务的命令，配置文件在环境变量中指定
CMD ["/bin/sh", "-c", "env; /app/bin/omni-worker"]
