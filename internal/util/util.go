package util

import (
	"context"
	"fmt"
	"omni_worker/pkg/media"
	"omni_worker/pkg/obs"
	"os"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	jsoniter "github.com/json-iterator/go"
)

const (
	DefaultFilePerm = 0644 // 推荐的文件权限：rw-r--r--
)

func ToJson(in any) string {
	out, err := jsoniter.MarshalToString(in)
	if err != nil {
		g.Log().Errorf(context.Background(), "ToJson failed: %v, input: %v", err, in)
		return ""
	}
	return out
}

func EnsureFile(filename string) error {
	err := retry.Do(func() error {
		fi, errF := os.Stat(filename)
		if errF != nil {
			return errF
		}
		if fi.IsDir() {
			return fmt.Errorf("file is a directory")
		}
		file, errF := os.OpenFile(filename, os.O_RDWR, DefaultFilePerm)
		if errF != nil {
			g.Log().Errorf(context.Background(), "open file failed, file: %v, err: %v", filename, errF)
			return errF
		}
		file.Sync() // 直接刷盘
		file.Close()
		return nil
	}, retry.Attempts(10), retry.Delay(2*time.Second), retry.DelayType(retry.FixedDelay))
	if err != nil {
		return err
	}
	return nil
}

func UploadAndEnsureMediaFile(localFile, targetObjectName string) error {
	err := retry.Do(func() error {
		_, errP := obs.UploadFile(localFile, targetObjectName)
		if errP != nil {
			return fmt.Errorf("upload file failed, err: %v", errP)
		}
		directUrl, _, err := obs.GetOBSClient().GetObjectUrl(targetObjectName)
		if err != nil {
			return fmt.Errorf("get object url failed, err: %v", err)
		}
		_, err = media.GetAudioVideoInfo(directUrl)
		if err != nil {
			return fmt.Errorf("校验合成音频后的视频失败, targetObjectName: %v, err: %s",
				targetObjectName, err.Error())
		}
		return nil
	}, retry.Attempts(10), retry.Delay(3*time.Second), retry.DelayType(retry.FixedDelay))
	if err != nil {
		return err
	}
	return nil
}

func EnsureOutputDirectory(outputPath ...string) error {
	for _, path := range outputPath {
		if _, err := os.Stat(path); os.IsNotExist(err) {
			if err := os.MkdirAll(path, os.ModePerm); err != nil {
				return fmt.Errorf("failed to create output directory, path: %v, err: %v", path, err)
			}
		}
	}
	return nil
}

func GetObjectNameByHttpsUrl(httpsUrl string) (string, error) {

	//兼容http协议以及带签名的链接
	if strings.HasPrefix(httpsUrl, "http://") {
		httpsUrl = strings.Replace(httpsUrl, "http://", "https://", 1)
	}
	prefix := "https://"
	pos := strings.Index(httpsUrl, prefix)
	if pos == -1 {
		return "", fmt.Errorf("url is not https url")
	}
	str := httpsUrl[pos+len(prefix)+1:]
	pos = strings.Index(str, "/")
	if pos == -1 {
		return "", fmt.Errorf("object name is not in url")
	}
	objectName := str[pos+1:]
	pos3 := strings.Index(objectName, "?")
	if pos3 == -1 {
		return objectName, nil
	}
	//去掉？号后面的内容
	return objectName[:pos3], nil
}
