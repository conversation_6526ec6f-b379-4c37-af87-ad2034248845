package util

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"go.uber.org/zap"
	"io"
	"net/http"
	"strconv"
	"strings"
)

var (
	styleMap = map[string]struct{}{
		"b":     struct{}{},
		"i":     struct{}{},
		"font":  struct{}{},
		"u":     struct{}{},
		"br":    struct{}{},
		"/b":    struct{}{},
		"/i":    struct{}{},
		"/font": struct{}{},
		"/u":    struct{}{},
		"/br":   struct{}{},
	}
)

type SrtItem struct {
	Idx        int32
	StartStr   string
	EndStr     string
	StartTime  int64
	EndTime    int64
	OriginText string
	TargetText string
	HasTrans   bool
	TextRect   []int32
	LastModify int64
}

type SrtItemSortType []*SrtItem

func (s SrtItemSortType) Len() int {
	return len(s)
}
func (s SrtItemSortType) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}
func (s SrtItemSortType) Less(i, j int) bool {
	return s[i].StartTime < s[j].StartTime
}

func parseSrtItem(idLine, timeLine, textLine string) *SrtItem {
	id, err := strconv.ParseInt(idLine, 10, 32)
	if err != nil {
		return nil
	}
	vec := strings.Split(timeLine, "-->")
	if len(vec) != 2 {
		return nil
	}

	item := &SrtItem{}
	item.Idx = int32(id)
	item.StartStr = strings.TrimSpace(vec[0])
	item.EndStr = strings.TrimSpace(vec[1])
	item.OriginText = strings.TrimSpace(textLine)
	return item
}

func ParseSrtWithContent(data []byte) ([]*SrtItem, []*SrtItem, error) {
	srts, err := ParseSrtV2(data)
	if err != nil {
		return nil, nil, err
	}
	//是否有译文字幕？
	tgtSrts := make([]*SrtItem, 0, len(srts))
	//只要有一条译文，那么就是有译文
	isTrans := false
	for _, val := range srts {
		if val.HasTrans {
			isTrans = true
		}
		item := &SrtItem{}
		item.Idx = val.Idx
		item.TargetText = val.TargetText
		item.OriginText = val.TargetText
		item.StartStr = val.StartStr
		item.EndStr = val.EndStr
		tgtSrts = append(tgtSrts, item)
	}
	if isTrans {
		return srts, tgtSrts, nil
	} else {
		return srts, nil, nil
	}
}

func ParseSrtWithUrl(ctx context.Context, url string) ([]*SrtItem, []*SrtItem, error) {
	resp, err := http.Get(url)
	if err != nil {
		//log.Fatal(err)
		return nil, nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	g.Log().Infof(ctx, "ParseSrtWithUrl", zap.String("url", url), zap.Int("len", len(body)))
	if err != nil {
		//log.Fatal(err)
		return nil, nil, err
	}

	return ParseSrtWithContent(body)
}

func isSpaceChar(ch byte) bool {
	if ch == ' ' || ch == '>' ||
		ch == '<' || ch == '{' || ch == '}' {
		return true
	}
	return false
}

func getNextTokenFrom(text string, pos int) string {
	arr := make([]byte, 0)
	isBegin := false
	for i := pos; i < len(text); i++ {
		if isSpaceChar(text[i]) {
			if isBegin {
				return string(arr)
			}
			continue
		} else {
			isBegin = true
			arr = append(arr, text[i])
		}
	}
	return string(arr)
}

func doEraseStyleTag(text string, from int) string {
	to := from
	for i := from + 1; i < len(text); i++ {
		if text[i] == ' ' {
			continue
		}
		if text[i] == '>' || text[i] == '}' {
			to = i
			break
		}
		if isSpaceChar(text[i]) {
			break
		}
	}
	if to == from {
		return text
	}
	prefix := ""
	if from > 0 {
		prefix = text[0:from]
	}
	suffix := text[to+1:]
	return prefix + suffix
}

func eraseStyleTag(text string, from int) (string, int) {
	newText := text
	//去除字幕中的样式标签，并返回新的字幕文本
	for i := from; i < len(text); i++ {
		if text[i] == '<' || text[i] == '{' {
			token := getNextTokenFrom(text, i+1)
			_, ok := styleMap[token]
			if ok {
				newText = doEraseStyleTag(text, i)
				return newText, i
			}
		}
	}
	return newText, len(text)
}

func parseStyleTag(text string) string {
	newText := ""
	counter := 1
	index := 0
	for {
		if counter > 1000 {
			break
		}
		newText, index = eraseStyleTag(text, index)
		if len(newText) == len(text) && index == len(text) {
			break
		}
		//index++
		text = newText
		counter++
	}
	return newText
}

func ParseSrt(ctx context.Context, data []byte) ([]*SrtItem, error) {

	//处理utf-8 with BOM
	if len(data) < 3 {
		g.Log().Errorf(ctx, "too small data for parse srt, len: %v", len(data))
		return make([]*SrtItem, 0), nil
	}
	if data[0] == 0xef && data[1] == 0xbb && data[2] == 0xbf {
		data = data[3:]
	}
	res := make([]*SrtItem, 0)
	scanner := bufio.NewScanner(bytes.NewReader(data))
	counter := 0
	flag := false
	flag = scanner.Scan()
	for {
		counter = counter + 1
		if flag {
			idLine := scanner.Text()
			flag = scanner.Scan()
			if flag {
				timeLine := scanner.Text()
				flag = scanner.Scan()
				if flag {
					textLine := scanner.Text()
					if textLine != "" {
						for i := 0; i < 100; i++ {
							flag2 := scanner.Scan() //读下一行
							if flag2 {
								str := scanner.Text()
								str = strings.Trim(str, " ")
								if str == "" { //下一行是空行
									break
								} else {
									textLine = textLine + " " + str
								}
							}
						}
					}
					item := parseSrtItem(idLine, timeLine, textLine)
					if item != nil {
						res = append(res, item)
					}
				} else {
					break
				}
			} else {
				break
			}
		} else {
			break
		}

		scanner.Scan() //跳过空行
		t := scanner.Text()
		limit := 0
		for {
			limit++
			if t == "" {
				scanner.Scan()
				t = scanner.Text()
			} else {
				break
			}
			if limit > 5 {
				break
			}
		}

		if counter > 100000 {
			break
		}
	}

	for _, val := range res {
		val.OriginText = parseStyleTag(val.OriginText)
	}

	return res, nil
}

// 解析双语字幕
func ParseSrtV2(data []byte) ([]*SrtItem, error) {

	//if ((0xef == (unsigned char)text[0]) && (0xbb == (unsigned char)text[1]) && (0xbf == (unsigned char)text[2]))
	if len(data) < 3 {
		return nil, fmt.Errorf("too small data for parse srt, len: %v", len(data))
	}
	if data[0] == 0xef && data[1] == 0xbb && data[2] == 0xbf {
		data = data[3:]
	}
	res := make([]*SrtItem, 0)
	scanner := bufio.NewScanner(bytes.NewReader(data))
	counter := 0
	flag := false
	flag = scanner.Scan()
	idx := 1
	for {
		counter = counter + 1

		if flag {
			idLine := scanner.Text()
			flag = scanner.Scan()
			if flag {
				timeLine := scanner.Text()
				flag = scanner.Scan()
				if flag {
					textLine := scanner.Text()
					oriText := textLine
					transText := ""
					currText := oriText
					transExist := false
					if textLine != "" {
						for i := 0; i < 100; i++ {
							flag2 := scanner.Scan() //读下一行
							if flag2 {
								str := scanner.Text()
								if str == "" { //下一行是空行
									if transExist {
										transText = currText
									}
									break
								} else {
									if str == "%%" {
										oriText = currText
										currText = ""
										transExist = true
									} else {
										currText = currText + " " + str
										if transExist {
											transText = currText
										}
									}
									//textLine = textLine + " " + str
								}
							}
						}
					}
					item := parseSrtItem(idLine, timeLine, oriText)
					if item != nil {
						if transExist {
							item.TargetText = transText
						}
						item.HasTrans = transExist
						item.Idx = int32(idx)
						idx++
						res = append(res, item)
					}
				} else {
					break
				}
			} else {
				break
			}
		} else {
			break
		}

		scanner.Scan() //跳过空行
		t := scanner.Text()
		limit := 0
		for {
			limit++
			if t == "" {
				scanner.Scan()
				t = scanner.Text()
			} else {
				break
			}
			if limit > 5 {
				break
			}
		}

		if counter > 100000 {
			break
		}
	}

	for _, val := range res {
		val.OriginText = parseStyleTag(val.OriginText)
		val.TargetText = parseStyleTag(val.TargetText)
	}

	return res, nil
}
