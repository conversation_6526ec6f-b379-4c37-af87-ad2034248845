package monitor

import (
	"strconv"
	"time"

	"omni_worker/pkg/config"

	"github.com/prometheus/client_golang/prometheus"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

var (
	// 定义一个Gauge指标用于记录正在执行的taskid
	taskInExecution = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "omni_worker_task_in_execution",
			Help: "Indicates if a specific task is in execution",
		},
		[]string{"task_type", "biz_type", "task_id"},
	)

	// 正在运行的worker数量
	workerRunCount = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "omni_worker_run_count",
			Help: "Number of workers running",
		},
		[]string{"task_type"},
	)

	// 监控任务执行的平均耗时
	taskExecuteCostSeconds = prometheus.NewSummaryVec(
		prometheus.SummaryOpts{
			Name:       "omni_worker_task_execution_cost_seconds",
			Help:       "Duration of worker task execution in seconds",
			Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01}, // P50 和 P90 的目标
		},
		[]string{"task_type", "biz_type"},
	)

	taskExecuteCostSecondsHist = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "omni_worker_task_execution_cost_seconds_hist",
			Help:    "Histogram of worker task execution in seconds",
			Buckets: prometheus.ExponentialBuckets(5, 1.5, 20), // 起始值 5 秒，每个桶增长 1.5 倍，共 20 个桶，覆盖从 5 秒到约 3 小时的等待时间
		},
		[]string{"task_type", "biz_type"},
	)

	// 监控任务吞吐量
	taskCapacity = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "omni_worker_task_capacity",
			Help: "Task capacity of worker",
		},
		[]string{"task_type", "biz_type"},
	)

	// 监控第三方接口调用情况
	thirdPartyApiCallTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "omni_worker_third_party_api_calls_total",
			Help: "Total number of third-party API calls",
		},
		[]string{"task_type", "api_name", "status"}, // status: success/failed
	)

	// 监控第三方接口调用耗时
	thirdPartyApiCallDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "omni_worker_third_party_api_duration_seconds",
			Help:    "Duration of third-party API calls in seconds",
			Buckets: prometheus.ExponentialBuckets(0.1, 2, 10), // 从0.1秒到约102秒
		},
		[]string{"task_type", "api_name"},
	)

	// 监控第三方接口每分钟调用次数
	thirdPartyApiCallRate = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "omni_worker_third_party_api_call_rate",
			Help: "Rate of third-party API calls per minute",
		},
		[]string{"task_type", "api_name"},
	)

	// 监控第三方接口频率限制触发次数
	thirdPartyApiRateLimitHits = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "omni_worker_third_party_api_rate_limit_hits_total",
			Help: "Total number of rate limit hits for third-party API calls",
		},
		[]string{"task_type", "api_name"},
	)

	// 监控第三方接口重试次数
	thirdPartyApiRetryHistogram = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "omni_worker_third_party_api_retry_count",
			Help:    "Histogram of retry attempts for third-party API calls",
			Buckets: []float64{0, 1, 2, 3, 4, 5, 10}, // 重试次数的分布区间
		},
		[]string{"task_type", "api_name"},
	)

	// 监控重试达到最大限制的告警
	retryLimitReachedAlert = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "omni_worker_retry_limit_reached_total",
			Help: "Total number of times retry limit was reached",
		},
		[]string{"task_type"},
	)
)

func Init() {
	prometheus.MustRegister(taskInExecution)
	prometheus.MustRegister(workerRunCount)
	prometheus.MustRegister(taskExecuteCostSeconds)
	prometheus.MustRegister(taskCapacity)
	prometheus.MustRegister(taskExecuteCostSecondsHist)

	// 注册新的指标
	prometheus.MustRegister(thirdPartyApiCallTotal)
	prometheus.MustRegister(thirdPartyApiCallDuration)
	prometheus.MustRegister(thirdPartyApiCallRate)
	prometheus.MustRegister(thirdPartyApiRateLimitHits)
	prometheus.MustRegister(thirdPartyApiRetryHistogram)
	prometheus.MustRegister(retryLimitReachedAlert)
}

func SetWorkerRunCount(taskType omniEngine.TaskType, count float64) {
	taskTypeStr := config.GetTaskNameByType(taskType)

	workerRunCount.WithLabelValues(taskTypeStr).Set(count)
}

func DecWorkerRunCount(taskType omniEngine.TaskType) {
	taskTypeStr := config.GetTaskNameByType(taskType)
	workerRunCount.WithLabelValues(taskTypeStr).Dec()
}

func StartTask(taskType omniEngine.TaskType, bizType omniEngine.VoiceBusinessType, taskID int64) {
	labels := prometheus.Labels{"task_type": config.GetTaskNameByType(taskType), "biz_type": strconv.Itoa(int(bizType)), "task_id": strconv.FormatInt(taskID, 10)}
	taskInExecution.With(labels).Set(1)
}

func EndTask(taskType omniEngine.TaskType, bizType omniEngine.VoiceBusinessType, taskID int64) {
	labels := prometheus.Labels{"task_type": config.GetTaskNameByType(taskType), "biz_type": strconv.Itoa(int(bizType)), "task_id": strconv.FormatInt(taskID, 10)}

	taskInExecution.With(labels).Set(0)
	taskInExecution.Delete(labels)
}

func ObserveTaskExecuteCostSeconds(taskType omniEngine.TaskType, bizType omniEngine.VoiceBusinessType, costTime float64, quantityStr string, status omniEngine.TaskStatus) {
	if status != omniEngine.TaskStatus_COMPLETED {
		return
	}
	labels := prometheus.Labels{"task_type": config.GetTaskNameByType(taskType), "biz_type": strconv.Itoa(int(bizType))}
	taskExecuteCostSeconds.With(labels).Observe(costTime)
	taskExecuteCostSecondsHist.With(labels).Observe(costTime)
	quantity, _ := strconv.ParseFloat(quantityStr, 64)
	if quantity != 0 && costTime != 0 {
		taskCapacity.With(labels).Set(quantity / costTime)
	}
}

// ObserveThirdPartyApiCall 记录第三方API调用情况
func ObserveThirdPartyApiCall(taskType omniEngine.TaskType, apiName string, duration time.Duration, success bool) {
	taskTypeStr := config.GetTaskNameByType(taskType)
	status := "success"
	if !success {
		status = "failed"
	}

	// 记录调用总次数
	thirdPartyApiCallTotal.WithLabelValues(taskTypeStr, apiName, status).Inc()

	// 记录调用耗时
	thirdPartyApiCallDuration.WithLabelValues(taskTypeStr, apiName).Observe(duration.Seconds())

	// 更新每分钟调用率
	thirdPartyApiCallRate.WithLabelValues(taskTypeStr, apiName).Set(1) // 每次调用设置为1，依赖Prometheus的rate()函数计算实际速率
}

// RecordThirdPartyApiRateLimit 记录频率限制触发
func RecordThirdPartyApiRateLimit(taskType omniEngine.TaskType, apiName string) {
	taskTypeStr := config.GetTaskNameByType(taskType)
	thirdPartyApiRateLimitHits.WithLabelValues(taskTypeStr, apiName).Inc()
}

// RecordThirdPartyApiRetryCount 记录API重试次数
func RecordThirdPartyApiRetryCount(taskType omniEngine.TaskType, apiName string, retryCount int) {
	taskTypeStr := config.GetTaskNameByType(taskType)
	thirdPartyApiRetryHistogram.WithLabelValues(taskTypeStr, apiName).Observe(float64(retryCount))
}

// RecordRetryLimitReached 记录重试达到最大限制
func RecordRetryLimitReached(taskType omniEngine.TaskType) {
	taskTypeStr := config.GetTaskNameByType(taskType)
	retryLimitReachedAlert.WithLabelValues(
		taskTypeStr,
	).Inc()
}
