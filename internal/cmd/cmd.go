package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"omni_worker/internal/client"
	"omni_worker/internal/consts"
	"omni_worker/internal/monitor"
	"omni_worker/internal/workers"
	"omni_worker/pkg/config"
	"omni_worker/pkg/obs"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
)

// Main is the main command.
var Main = gcmd.Command{
	Name:  "main",
	Usage: "main",
	Brief: "omni-worker start",
	Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
		// 加载配置
		configFilePath := parser.GetOpt("config").String()
		cfg := config.InitConfig(ctx, configFilePath)
		// 初始化日志
		trace.InitLog(cfg.Logger.File, cfg.Logger.Path, "", []interface{}{
			consts.TaskId,
			consts.ContextKeyEngineTaskId,
		})
		// 初始化prometheus
		monitor.Init()
		// 初始化OBS客户端
		obs.InitOBSClient()
		// 初始化grpc
		client.InitGRPCClient(ctx, cfg)
		// 启动worker
		taskTypeEnumStr := os.Getenv(consts.LaunchTaskType)
		healthCheckSw := os.Getenv(consts.HealthCheckSwitch)
		taskTypeEnumList := strings.Split(taskTypeEnumStr, ",")
		workers.Run(ctx, taskTypeEnumList, healthCheckSw)
		// 启动http服务
		s := g.Server()
		s.SetAddr(cfg.Server.HTTPAddress)
		s.BindHandler("/health", func(r *ghttp.Request) {
			r.Response.WriteJson(g.Map{
				"time": time.Now().Format(time.RFC3339),
			})
		})
		s.BindHandler("/metrics", func(r *ghttp.Request) {
			promhttp.Handler().ServeHTTP(r.Response.Writer, r.Request)
		})
		s.Run()
		return nil
	},
}

func InitLog(file, logDir, version string, ctxKyes []interface{}) {
	type JsonOutputsForLogger struct {
		Time      string `json:"ts"`
		Caller    string `json:"caller"`
		RequestId string `json:"request_id"`
		Level     string `json:"level"`
		Msg       string `json:"msg"`
		Version   string `json:"version"`
	}
	// LoggingJsonHandler is a example handler for logging JSON format content.
	var LoggingJsonHandler glog.Handler = func(ctx context.Context, in *glog.HandlerInput) {
		jsonForLogger := JsonOutputsForLogger{
			Time:      in.TimeFormat,
			Level:     gstr.Trim(in.LevelFormat, "[]"),
			RequestId: in.TraceId,
			Caller:    gstr.Trim(fmt.Sprintf("%s%s", in.CallerPath, in.CallerFunc), ":"),
			Msg:       gstr.Trim(in.ValuesContent()),
			Version:   version,
		}
		if rId, ok := ctx.Value(trace.ReqId).(string); ok {
			jsonForLogger.RequestId = rId
		}
		jsonBytes, err := json.Marshal(jsonForLogger)
		if err != nil {
			_, _ = os.Stderr.WriteString(err.Error())
			return
		}
		in.Buffer.Write(jsonBytes)
		in.Buffer.WriteString("\n")
		in.Next(ctx)
	}
	g.Log().SetHandlers(LoggingJsonHandler)
	cfg := g.Log().GetConfig()
	cfg.Level = glog.LEVEL_ALL
	cfg.Flags = cfg.Flags | glog.F_FILE_LONG | glog.F_CALLER_FN
	cfg.CtxKeys = append(cfg.CtxKeys, ctxKyes...)
	if logDir != "" {
		cfg.Path = logDir
		cfg.File = file
		cfg.StdoutPrint = false
	} else {
		cfg.StdoutPrint = true
	}
	g.Log().SetConfig(cfg)
}
