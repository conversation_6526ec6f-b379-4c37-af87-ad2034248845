package workers

import (
	"context"
	"errors"
	"fmt"
	"omni_worker/internal/util"
	"omni_worker/pkg/config"
	"strconv"
	"time"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

const (
	pollInterval   = 5 * time.Second
	pollTimeout    = 5 * time.Minute
	taskSuccess    = "20000000"
	taskInProgress = "20000001"

	taskQueued     = "20000002"
	qpsLimit       = "45000000"
	XApiAppKey     = "X-Api-App-Key"
	XApiAccessKey  = "X-Api-Access-Key"
	XApiResourceId = "X-Api-Resource-Id"
	XApiRequestId  = "X-Api-Request-Id"
	XTtLogid       = "X-Tt-Logid"
	XVolcUser      = "X-Volc-User"
	XApiMessage    = "X-Api-Message"
	XApiStatusCode = "X-Api-Status-Code"
)

type VolcengineAsrRequest struct {
	TaskID  int64               `json:"task_id"`
	User    VolcengineAsrUser   `json:"user,omitempty"`
	Audio   VolcengineAsrAudio  `json:"audio,omitempty"`
	Request VolcengineAsrConfig `json:"request,omitempty"`
}

type VolcengineAsrUser struct {
	UID string `json:"uid,omitempty"`
}

type VolcengineAsrAudio struct {
	URL     string `json:"url,omitempty"`
	Format  string `json:"format,omitempty"`
	Codec   string `json:"codec,omitempty"`
	Rate    int    `json:"rate,omitempty"`
	Bits    int    `json:"bits,omitempty"`
	Channel int32  `json:"channel,omitempty"`
}

type VolcengineAsrConfig struct {
	ModelName         string              `json:"model_name,omitempty"`
	ShowUtterances    bool                `json:"show_utterances,omitempty"`
	EnableSpeakerInfo bool                `json:"enable_speaker_info,omitempty"`
	Corpus            VolcengineAsrCorpus `json:"corpus,omitempty"`
}

type VolcengineAsrCorpus struct {
	CorrectTableName string `json:"correct_table_name,omitempty"`
	Context          string `json:"context,omitempty"`
}

// VolcengineAsrResponse 表示整个 JSON 响应结构
type VolcengineAsrResponse struct {
	AudioInfo VolcengineAsrAudioInfo `json:"audio_info,omitempty"`
	Result    VolcengineAsrResult    `json:"result,omitempty"`
}

// VolcengineAsrAudioInfo 表示音频相关信息
type VolcengineAsrAudioInfo struct {
	Duration int64 `json:"duration,omitempty"` // 音频时长（毫秒）
}

// VolcengineAsrResult 表示识别结果，包括全文、分句和补充信息
type VolcengineAsrResult struct {
	Additions  VolcengineAsrAdditions   `json:"additions,omitempty"`
	Text       string                   `json:"text,omitempty"`       // 全部识别文本
	Utterances []VolcengineAsrUtterance `json:"utterances,omitempty"` // 分句信息
}

// VolcengineAsrAdditions 是 result 中的补充信息
type VolcengineAsrAdditions struct {
	Duration string `json:"duration,omitempty"` // 补充的音频时长（字符串形式）
}

// VolcengineAsrUtterance 表示一句话的识别内容及词信息
type VolcengineAsrUtterance struct {
	Additions VolcengineAsrUtteranceAdditions `json:"additions,omitempty"`
	StartTime int64                           `json:"start_time"`      // 起始时间（毫秒）
	EndTime   int64                           `json:"end_time"`        // 结束时间（毫秒）
	Text      string                          `json:"text,omitempty"`  // 句子文本
	Words     []VolcengineAsrWord             `json:"words,omitempty"` // 词信息列表
}

// VolcengineAsrUtteranceAdditions 是一句话中的附加属性，如说话人信息
type VolcengineAsrUtteranceAdditions struct {
	Speaker string `json:"speaker"` // 说话人编号，如 "1", "2"
}

// VolcengineAsrWord 表示单个词的信息
type VolcengineAsrWord struct {
	Text       string `json:"text,omitempty"` // 词文本
	StartTime  int64  `json:"start_time"`     // 起始时间（毫秒）
	EndTime    int64  `json:"end_time"`       // 结束时间（毫秒）
	Confidence int    `json:"confidence"`     // 置信度（目前示例中恒为 0）
}
type VolcengineAsrWorker struct {
	*BaseWorker
	submitUrl string
	resultUrl string
	taskType  omniEngine.TaskType
}

func NewVolcengineAsrWorker(taskType omniEngine.TaskType) *VolcengineAsrWorker {
	cfg := config.GetConfig()
	return &VolcengineAsrWorker{
		BaseWorker: NewBaseWorker(),
		submitUrl:  cfg.VolcengineAsrConfigConfig.SubmitUrl,
		resultUrl:  cfg.VolcengineAsrConfigConfig.ResultUrl,
		taskType:   taskType,
	}
}

func (a *VolcengineAsrWorker) GetWorkerName() string {
	return config.GetTaskNameByType(a.taskType)
}

func (a *VolcengineAsrWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (a *VolcengineAsrWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting VolcengineAsr task: %d", t.Id)
	channel := t.GetAudioVolcengineAsrInputContent().GetChannel()
	if channel == 0 {
		channel = 1
	}
	request := VolcengineAsrRequest{
		TaskID: t.Id,
		User: VolcengineAsrUser{
			UID: fmt.Sprintf("%d", t.TenantId),
		},
		Audio: VolcengineAsrAudio{
			URL:     t.GetAudioVolcengineAsrInputContent().AudioUrl,
			Channel: channel, // 默认单声道
			Format:  t.GetAudioVolcengineAsrInputContent().GetFormat(),
			Codec:   t.GetAudioVolcengineAsrInputContent().GetCodec(),
		},
		Request: VolcengineAsrConfig{
			ModelName:         "bigmodel",
			ShowUtterances:    true,
			EnableSpeakerInfo: true,
			Corpus: VolcengineAsrCorpus{
				CorrectTableName: "",
				Context:          "",
			},
		},
	}
	cost := a.getCost(ctx, t)

	return a.handleVolcengineASRTask(ctx, request, cost)
}

func (a *VolcengineAsrWorker) getCost(ctx context.Context, t *omniEngine.Task) int64 {
	extra := t.GetExtra()
	if extra == nil {
		return 0
	}

	taskCostStr, ok := extra["task_cost"]
	if !ok {
		return 0
	}

	parsed, err := strconv.ParseInt(taskCostStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "VolcengineASR task cost parse failed: %v", err)
		return 0
	}

	return parsed
}

func (a *VolcengineAsrWorker) handleVolcengineASRTask(ctx context.Context, request VolcengineAsrRequest, cost int64) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call VolcengineAsr request: %+v cost:%d", request, cost)
	if err := a.validateTask(request); err != nil {
		g.Log().Errorf(ctx, "Invalid VolcengineAsr task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid VolcengineAsr task: %v", err),
		}
	}
	reqHeader := map[string]string{
		XApiAppKey:     config.GetConfig().VolcengineAsrConfigConfig.ApiKey,
		XApiAccessKey:  config.GetConfig().VolcengineAsrConfigConfig.AccessKey,
		XApiResourceId: config.GetConfig().VolcengineAsrConfigConfig.ResourceId,
		XApiRequestId:  fmt.Sprintf("%d", request.TaskID),
		XVolcUser:      request.User.UID,
	}
	respHeader := make(map[string]string)
	var response VolcengineAsrResponse

	err := retry.Do(func() error {
		return a.postJsonWithHeader(ctx, a.submitUrl, request, &response, reqHeader, respHeader)
	},
		retry.Attempts(3),
		retry.Delay(3*time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	g.Log().Infof(ctx, "VolcengineAsr submit response header: %+v response %+v", respHeader, response)
	if err != nil {
		g.Log().Errorf(ctx, "VolcengineAsr task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("VolcengineAsr task failed: %v", err),
		}
	}
	statusCode := respHeader[XApiStatusCode]
	msg := respHeader[XApiMessage]
	logId := respHeader[XTtLogid]
	if statusCode != taskSuccess {
		g.Log().Errorf(ctx, "VolcengineAsr task failed: req:%v code:%s logId:%s  err %v", request, statusCode, logId, err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("VolcengineAsr submit task logId:%s msg:%s failed: %v", logId, msg, err),
		}
	}
	queryHeader := map[string]string{
		XApiAppKey:     config.GetConfig().VolcengineAsrConfigConfig.ApiKey,
		XApiAccessKey:  config.GetConfig().VolcengineAsrConfigConfig.AccessKey,
		XApiResourceId: config.GetConfig().VolcengineAsrConfigConfig.ResourceId,
		XApiRequestId:  fmt.Sprintf("%d", request.TaskID),
		XTtLogid:       logId,
	}

	// 轮训结果 超时时间
	queryResp := &VolcengineAsrResponse{}
	// 计算超时时间 根据 cost 判断 , 如果 cost <= 150  默认用5分钟
	// cost  > 150 时间  cost * 2 倍数
	// cost 最大不超过 3 小时
	timeOut := calculateTimeout(cost)
	err = a.pollResult(ctx, queryHeader, queryResp, timeOut, cost)
	if err != nil {
		g.Log().Errorf(ctx, "VolcengineAsr task polling failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("VolcengineAsr task polling failed: %s", err.Error()),
		}
	}

	g.Log().Infof(ctx, "Call VolcengineAsr query response: %s", util.ToJson(queryResp))
	return a.handleTaskResponse(ctx, request, queryResp)
}

func calculateTimeout(cost int64) time.Duration {
	const (
		maxTimeout = 3 * time.Hour
	)

	if cost <= 150 {
		return pollTimeout
	}

	// cost > 150，乘以2秒
	timeout := time.Duration(cost*2) * time.Second

	if timeout > maxTimeout {
		return maxTimeout
	}

	return timeout
}

func (a *VolcengineAsrWorker) pollResult(
	ctx context.Context,
	queryHeader map[string]string,
	response any,
	timeOut time.Duration,
	cost int64,
) error {
	g.Log().Infof(ctx, "VolcengineAsr polling start, timeout: %s", timeOut)
	start := time.Now()
	// 计算下合理的轮询间隔
	interval := calculatePollInterval(cost)
	for {
		// 判断超时
		if time.Since(start) > timeOut {
			return fmt.Errorf("VolcengineAsr polling timeout %s", timeOut)
		}
		respHeader := make(map[string]string)
		// 请求接口
		err := retry.Do(func() error {
			return a.postJsonWithHeader(ctx, a.resultUrl, map[string]string{}, &response, queryHeader, respHeader)
		},
			retry.Attempts(3),
			retry.Delay(3*time.Second),
			retry.MaxDelay(30*time.Second),
			retry.DelayType(retry.BackOffDelay), // 指数退避
		)
		if err != nil {
			g.Log().Errorf(ctx, "VolcengineAsr polling failed: req:%v err: %v", queryHeader, err)
			return errors.New("VolcengineAsr polling failed: " + err.Error())
		}

		// 提取 code 信息
		statusCode := respHeader[XApiStatusCode]
		msg := respHeader[XApiMessage]
		logId := respHeader[XTtLogid]

		g.Log().Infof(ctx, "VolcengineAsr polling: code=%s msg=%s logId=%s", statusCode, msg, logId)

		switch statusCode {
		case taskSuccess:
			return nil
		case taskInProgress, taskQueued:
			// 任务尚未完成，继续轮询
			time.Sleep(interval)
			continue
		case qpsLimit:
			// QPS 限制，稍后重试
			g.Log().Infof(ctx, "VolcengineAsr polling hit QPS limit, retrying after delay")
			time.Sleep(interval * 2)
			continue
		default:
			// 明确失败状态
			return fmt.Errorf("VolcengineAsr polling failed: code=%s msg=%s logId=%s", statusCode, msg, logId)
		}
	}
}

func calculatePollInterval(cost int64) time.Duration {
	// 根据音频长度（cost秒）设置固定轮询间隔
	switch {
	case cost <= 60: // 30秒以内
		return pollInterval // 5秒
	case cost <= 120: // 2分钟以内
		return 10 * time.Second // 10秒
	case cost <= 300: // 2-5分钟以内
		return 15 * time.Second
	case cost <= 900: // 5-15分钟以内
		return 30 * time.Second
	case cost <= 1800: // 30分钟以内
		return 1 * time.Minute
	default: // 超过30分钟
		return 3 * time.Minute
	}
}

func (a *VolcengineAsrWorker) validateTask(request VolcengineAsrRequest) error {
	if !isValidUrl(request.Audio.URL) {
		return errors.New("invalid Audio.URL: must be a valid HTTP/HTTPS Url")
	}
	if request.User.UID == "" {
		return errors.New("uid cannot be empty")
	}
	if request.TaskID <= 0 {
		return errors.New("TaskID cannot be empty")
	}
	return nil
}

func (a *VolcengineAsrWorker) handleTaskResponse(ctx context.Context, request VolcengineAsrRequest, response *VolcengineAsrResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 0: 成功，1: 失败
	if response != nil && len(response.Result.Text) > 0 {
		g.Log().Infof(ctx, "VolcengineAsr task %d completed successfully", request.TaskID)
		taskOutputContent := &TaskOutputContent{
			Code:    CodeCompleted,
			Message: StatusCompleted,
			Data:    response,
		}
		return omniEngine.TaskStatus_COMPLETED, taskOutputContent
	} else {
		errorMsg := fmt.Sprintf("VolcengineAsr task %d failed with  message: %+v", request.TaskID, response)
		g.Log().Errorf(ctx, errorMsg)
		taskOutputContent := &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: errorMsg,
		}
		return omniEngine.TaskStatus_FAILED, taskOutputContent
	}
}
