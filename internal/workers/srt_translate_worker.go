package workers

import (
	"context"
	"encoding/json"
	"fmt"

	"omni_worker/pkg/config"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type SRTTranslateWorker struct {
	taskType omniEngine.TaskType
	*BaseWorker
	srtTranslateUrl string
}

func NewSRTTranslateWorker(taskType omniEngine.TaskType) *SRTTranslateWorker {
	cfg := config.GetConfig().SRTTranslateService
	return &SRTTranslateWorker{
		taskType:        taskType,
		BaseWorker:      NewBaseWorker(),
		srtTranslateUrl: cfg.SrtTranslateUrl,
	}
}

type RetranslateSubtitleSingle struct {
	Id   int32  `json:"id"`
	Text string `json:"text"`
}

type RetranslateFullText struct {
	TaskId    int64                       `json:"task_id"`
	Subtitles []RetranslateSubtitleSingle `json:"subtitles"`
}

// 重新翻译字幕 AI请求
type ReTranslateSubtitleServiceReq struct {
	FromLang string              `json:"from_lang"`
	ToLang   string              `json:"to_lang"`
	Text     string              `json:"text"`
	JsonText RetranslateFullText `json:"text_json"`
}

type RetranslateServiceResult struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		FromLang        string              `json:"from_lang"`
		ToLang          string              `json:"to_lang"`
		Query           string              `json:"query"`
		Translation     string              `json:"translation"`
		TranslateResult interface{}         `json:"trans_result"`
		JsonResult      RetranslateFullText `json:"trans_json_result"`
		ErrorCode       int                 `json:"error_code"`
	} `json:"data"`
}

func (w *SRTTranslateWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting SRT task: %d, SRTTranslateWorker:%v", t.Id, w)

	// Get task input content
	inputContentStr := t.GetCommonInputContent()
	if inputContentStr == "" {
		g.Log().Errorf(ctx, "Failed to get task input content, task:%+v", t)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "Failed to get task input content",
		}
	}
	req := &ReTranslateSubtitleServiceReq{}
	err := json.Unmarshal([]byte(inputContentStr), req)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to unmarshal task input content, task:%+v, err:%v", t, err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to unmarshal task input content: %v", err),
		}
	}
	g.Log().Debugf(ctx, "Parsed task input content: %+v", req)
	req.JsonText.TaskId = t.Id

	resp := &RetranslateServiceResult{}
	err = w.PostJSON(ctx, w.srtTranslateUrl, req, resp)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to retranslate subtitle, task:%+v, err:%v", t, err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to retranslate subtitle: %v", err),
		}
	}
	g.Log().Debugf(ctx, "Retranslate subtitle response: %+v", resp)
	if resp.Data.ErrorCode != 0 {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to retranslate subtitle: %v", resp.Data.ErrorCode),
		}
	}
	if resp.Data.JsonResult.TaskId != t.Id {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("TaskId inequal, resId: %d, reqId: %d", resp.Data.JsonResult.TaskId, t.Id),
		}
	}
	if len(resp.Data.JsonResult.Subtitles) != len(req.JsonText.Subtitles) {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Return len inequal, resLen: %d, reqLen: %d", len(resp.Data.JsonResult.Subtitles), len(req.JsonText.Subtitles)),
		}
	}

	g.Log().Infof(ctx, "srt task completed: %d", t.Id)
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    CodeCompleted,
		Message: StatusCompleted,
		Data:    resp,
	}
}

func (w *SRTTranslateWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *SRTTranslateWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}
