package workers

import (
	"context"
	"errors"
	"fmt"
	"math"
	"net/url"
	"omni_worker/internal/client"
	"omni_worker/internal/monitor"
	"omni_worker/pkg/config"
	"omni_worker/pkg/media"
	"omni_worker/pkg/obs"
	"os"
	"time"

	"github.com/samber/lo"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

// ThirdPartyTTSRequest 第三方TTS请求结构
type ThirdPartyTTSRequest struct {
	Type             string         `json:"type"`
	UserID           int64          `json:"user_id"`
	TaskID           int64          `json:"task_id"`
	SourceUrl        string         `json:"source_url"`
	TargetUrl        string         `json:"target_url"`
	SourceText       string         `json:"source_text"`
	TargetText       string         `json:"target_text"`
	SourceLang       string         `json:"source_lang"`
	TargetLang       string         `json:"target_lang"`
	IsTrans          bool           `json:"is_trans"`
	Speed            float32        `json:"speed"`
	SpeakerName      string         `json:"speaker_name"`
	VolumeNorm       int32          `json:"volume_norm"`
	TargetDuration   int64          `json:"target_duration,omitempty"`
	Emotion          string         `json:"emotion,omitempty"`            // 情感，如happy, sad等
	SpeakerAudioList []SpeakerAudio `json:"speaker_audio_list,omitempty"` // 说话人音频列表
}

// MinimaxTTSRequest 泼墨体TTS请求结构
type MinimaxTTSRequest struct {
	Model          string                 `json:"model"`
	Input          string                 `json:"input"`
	Voice          string                 `json:"voice"`
	Speed          float32                `json:"speed"`
	ResponseFormat string                 `json:"response_format"`
	ModelExtra     map[string]interface{} `json:"model_extra"`
}

// MinimaxTTSResponse 泼墨体TTS响应结构
type MinimaxTTSResponse struct {
	Code       int           `json:"code"`
	RequestID  string        `json:"request_id"`
	URL        string        `json:"url"`
	DurationMs int64         `json:"duration_ms"`
	ResultUrl  string        `json:"result_url"`
	Error      *MinimaxError `json:"error,omitempty"`
}

type MinimaxErrorInfo struct {
	Error *MinimaxError `json:"error,omitempty"`
}

type MinimaxError struct {
	Code    int              `json:"code"`
	Message string           `json:"message"`
	Details []*MinimaxDetail `json:"details,omitempty"`
	TraceID string           `json:"trace_id"`
}

type MinimaxDetail struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// ThirdPartyTTSResponse 通用第三方TTS响应结构
type ThirdPartyTTSResponse struct {
	Code      int                    `json:"code"`
	Message   string                 `json:"message"`
	TaskID    int64                  `json:"task_id"`
	Duration  float64                `json:"duration"`
	URL       string                 `json:"url"`
	RequestID string                 `json:"request_id"`
	ExtraData map[string]interface{} `json:"extra_data"`
}

// ThirdPartyTTSWorker 第三方TTS Worker
type ThirdPartyTTSWorker struct {
	*BaseWorker
	taskType          omniEngine.TaskType
	thirdPartyType    string
	thirdPartyTTSUrl  string
	thirdPartyAuthKey string
	thirdPartyModel   string
	retryConfig       config.RetryConfig
}

// NewThirdPartyTTSWorker 创建第三方TTS Worker
func NewThirdPartyTTSWorker(taskType omniEngine.TaskType) *ThirdPartyTTSWorker {
	return GetThirdPartyWorkerByTaskType(taskType)
}

// GetThirdPartyWorkerByTaskType 根据任务类型获取对应的第三方TTS Worker
func GetThirdPartyWorkerByTaskType(taskType omniEngine.TaskType) *ThirdPartyTTSWorker {
	cfg := config.GetConfig()

	ttsWorker := &ThirdPartyTTSWorker{
		BaseWorker: NewBaseWorker(),
		taskType:   taskType,
	}
	var model string
	defer concurrent.GoSafe(func() {
		client.GetGRPCMgr().GetOmniEngineClient(context.Background(), cfg.OmniEngineGRPCConfig.DefaultCluster).ReportAlgorithmVersion(context.Background(), ttsWorker.taskType, model)
	})
	// 根据不同的taskType配置不同的第三方服务
	switch taskType {
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_MINIMAX:
		// 检查配置映射是否存在
		if cfg.ThirdPartyTTSService.TTSConfigMap == nil {
			g.Log().Errorf(context.Background(), "TTSConfigMap is nil")
			return ttsWorker
		}

		ttsConfig, ok := cfg.ThirdPartyTTSService.TTSConfigMap[taskType]
		if !ok {
			g.Log().Errorf(context.Background(), "tts config not found for task type: %v", taskType)
			return ttsWorker
		}

		ttsWorker.thirdPartyTTSUrl = ttsConfig.Url
		ttsWorker.thirdPartyAuthKey = ttsConfig.AuthKey
		ttsWorker.thirdPartyModel = ttsConfig.Model
		ttsWorker.retryConfig = ttsConfig.RetryConfig
		model = ttsConfig.Model
		// URL 检查
		if !isValidThirdPartyUrl(ttsWorker.thirdPartyTTSUrl) {
			g.Log().Errorf(context.Background(), "Invalid ThirdParty TTS URL: %s", ttsWorker.thirdPartyTTSUrl)
			return ttsWorker
		}

		// 必要参数检查
		if ttsWorker.thirdPartyAuthKey == "" {
			g.Log().Errorf(context.Background(), "AuthKey is empty")
			return ttsWorker
		}
		return ttsWorker
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_11LABS:
		cfg := config.GetConfig()
		model = cfg.ElevenLabsConfig.DefaultModelId
		return ttsWorker
	default:
		g.Log().Errorf(context.Background(), "Unsupported third-party TTS task type: %v", taskType)
		return ttsWorker
	}
}

func (w *ThirdPartyTTSWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *ThirdPartyTTSWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *ThirdPartyTTSWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 基础参数检查
	if t == nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "task is nil",
		}
	}

	if t.GetTtsTaskInputContent() == nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "tts task input content is nil",
		}
	}

	g.Log().Infof(ctx, "Starting ThirdParty TTS task: %d, type: %s", t.Id, w.thirdPartyType)

	ttsRequest := ThirdPartyTTSRequest{
		Type:           t.GetTtsTaskInputContent().Type,
		UserID:         t.GetTenantId(),
		TaskID:         t.GetId(),
		SourceUrl:      t.GetTtsTaskInputContent().GetSourceUrl(),
		TargetUrl:      t.GetTtsTaskInputContent().GetTargetUrl(),
		SourceText:     t.GetTtsTaskInputContent().GetSourceText(),
		TargetText:     t.GetTtsTaskInputContent().GetTargetText(),
		SourceLang:     t.GetTtsTaskInputContent().GetSourceLang(),
		TargetLang:     t.GetTtsTaskInputContent().GetTargetLang(),
		IsTrans:        t.GetTtsTaskInputContent().GetIsTrans(),
		Speed:          t.GetTtsTaskInputContent().GetSpeed(),
		SpeakerName:    t.GetTtsTaskInputContent().GetSpeakerName(),
		VolumeNorm:     t.GetTtsTaskInputContent().GetVolumeNorm(),
		TargetDuration: t.GetTtsTaskInputContent().GetTargetDuration(),
	}
	if t.GetTtsTaskInputContent().GetSpeakerAudioList() != nil {
		ttsRequest.SpeakerAudioList = lo.Map(t.GetTtsTaskInputContent().GetSpeakerAudioList(), func(item *omniEngine.SpeakerAudioInfo, _ int) SpeakerAudio {
			return SpeakerAudio{
				Url:   item.GetUrl(),
				Score: item.GetScore(),
			}
		})
	}
	if err := w.validateTask(ttsRequest); err != nil {
		g.Log().Errorf(ctx, "Invalid ThirdParty TTS task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid ThirdParty TTS task: %v", err),
		}
	}

	// 根据不同的第三方类型处理请求
	switch w.taskType {
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_MINIMAX:
		return w.handleMinimaxTTSTask(ctx, ttsRequest)
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_11LABS:
		return w.handleElevenLabsTTSTask(ctx, ttsRequest)
	// 可以添加更多第三方服务类型的case
	default:
		errMsg := fmt.Sprintf("Unsupported third-party type: %s", w.thirdPartyType)
		g.Log().Errorf(ctx, errMsg)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: errMsg,
		}
	}
}

func (w *ThirdPartyTTSWorker) handleElevenLabsTTSTask(ctx context.Context, ttsRequest ThirdPartyTTSRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	cfg := config.GetConfig()
	// 克隆音色
	sourceUrl := ttsRequest.SourceUrl
	// 替换成优质的音色
	if len(ttsRequest.SpeakerAudioList) > 0 {
		sourceUrl = ttsRequest.SpeakerAudioList[0].Url
	}
	tempFile, err := w.downloadAudioToTempFile(ctx, sourceUrl, true)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to download audio file: %s, file: %s", err.Error(), ttsRequest.SourceUrl)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to download audio file: %s, file: %s", err.Error(), ttsRequest.SourceUrl),
		}
	}
	defer os.Remove(tempFile) // 确保临时文件被删除
	tempFile, err = media.ProcessAudioDuration(ctx, tempFile, 1, 30)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to process audio duration: %s", err.Error())
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to process audio duration: %s", err.Error()),
		}
	}
	addVoiceResp, err := w.ElevenLabsAddVoice(ctx, &ElevenLabsAddVoiceRequest{
		Name:     fmt.Sprintf("%d_%d", ttsRequest.UserID, ttsRequest.TaskID),
		FilePath: tempFile,
	})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to add voice: %s", err.Error())
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to add voice: %s", err.Error()),
		}
	}
	// 每次使用完删除克隆的音色
	defer func() {
		if addVoiceResp == nil || addVoiceResp.VoiceID == "" {
			return
		}
		err = w.ElevenLabsDeleteVoice(ctx, &ElevenLabsDeleteVoiceRequest{
			VoiceID: addVoiceResp.VoiceID,
		})
		if err != nil {
			g.Log().Errorf(ctx, "Failed to delete voice: %s, err: %s", addVoiceResp.VoiceID, err.Error())
		}
	}()
	// 生成tts
	modelId := cfg.ElevenLabsConfig.GetTTSModelId(ttsRequest.TargetLang)
	g.Log().Infof(ctx, "Generate 11labs tts, voiceID: %s, lang_code: %s, model_id: %s", addVoiceResp.VoiceID, ttsRequest.TargetLang, modelId)
	ttsReq := &ElevenLabsTextToSpeechRequest{
		VoiceID: addVoiceResp.VoiceID,
		Text:    ttsRequest.TargetText,
		ModelID: modelId,
	}
	// eleven_multilingual_v2不支持传语言代码
	if modelId != cfg.ElevenLabsConfig.DefaultModelId {
		ttsReq.LanguageCode = ttsRequest.TargetLang
	}
	ttsTmpFilePath, err := w.ElevenLabsTextToSpeech(ctx, ttsReq)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate tts: %s, voiceID: %s, lang_code: %s, model_id: %s", err.Error(), addVoiceResp.VoiceID, ttsRequest.TargetLang, modelId)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to generate tts: %s, voiceID: %s, lang_code: %s, model_id: %s", err.Error(), addVoiceResp.VoiceID, ttsRequest.TargetLang, modelId),
		}
	}
	defer os.Remove(ttsTmpFilePath)
	// 上传到OBS
	obsURL, err := w.uploadToOBS(ctx, ttsTmpFilePath, ttsRequest.TargetUrl)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload audio to OBS: %s", err.Error())
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to upload audio to OBS: %s", err.Error()),
		}
	}
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    CodeCompleted,
		Message: StatusCompleted,
		Data: &ElevenLabsTTSResp{
			ResultUrl: obsURL,
		},
	}
}

// 处理泼墨体TTS任务
func (w *ThirdPartyTTSWorker) handleMinimaxTTSTask(ctx context.Context, ttsRequest ThirdPartyTTSRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 参数检查
	if ctx == nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "context is nil",
		}
	}

	if w == nil || w.BaseWorker == nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "worker is not properly initialized",
		}
	}

	// 检查配置是否存在
	cfg := config.GetConfig()
	if cfg == nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "config is nil",
		}
	}

	ttsConfig, ok := cfg.ThirdPartyTTSService.TTSConfigMap[w.taskType]
	if !ok {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("tts config not found for task type: %v", w.taskType),
		}
	}

	g.Log().Infof(ctx, "Call Minimax TTS request: %+v", ttsRequest)

	// 构建泼墨体TTS请求
	MinimaxRequest := MinimaxTTSRequest{
		Model:          w.thirdPartyModel,
		Input:          ttsRequest.TargetText,
		Voice:          ttsRequest.SpeakerName,
		Speed:          ttsRequest.Speed,
		ResponseFormat: ttsConfig.Format,
		ModelExtra:     ttsConfig.ModelExtra, // 从配置中获取ModelExtra
	}

	// 调用泼墨体TTS服务
	ttsResponse, err := w.callMinimaxTTSService(ctx, MinimaxRequest)
	if err != nil || ttsResponse == nil {
		g.Log().Errorf(ctx, "Minimax TTS task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Minimax TTS task failed: %v", err),
		}
	}

	g.Log().Infof(ctx, "Call Minimax TTS response: %+v", ttsResponse)
	// 验证响应中的URL是否有效
	if ttsResponse.URL == "" || !isValidThirdPartyUrl(ttsResponse.URL) {
		g.Log().Errorf(ctx, "Invalid Minimax TTS response, URL is empty or invalid")
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "Invalid Minimax TTS response, URL is empty or invalid",
		}
	}

	// 下载音频到临时文件
	tempFile, err := w.downloadAudioToTempFile(ctx, ttsResponse.URL, false)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to download audio file: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to download audio file: %v", err),
		}
	}
	defer os.Remove(tempFile) // 确保临时文件被删除

	// 生成OBS对象键
	objectKey := ttsRequest.TargetUrl
	if objectKey == "" {
		g.Log().Errorf(ctx, "Target URL is empty, task ID: %d", ttsRequest.TaskID)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "Target URL is empty",
		}
	}

	// 上传到OBS
	obsURL, err := w.uploadToOBS(ctx, tempFile, objectKey)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload audio to OBS: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to upload audio to OBS: %v", err),
		}
	}
	ttsResponse.ResultUrl = obsURL

	g.Log().Infof(ctx, "Minimax TTS task completed, task ID: %d, audio URL: %s", ttsRequest.TaskID, obsURL)
	// 返回完成状态
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    CodeCompleted,
		Message: StatusCompleted,
		Data:    ttsResponse,
	}
}

// 调用泼墨体TTS服务
func (w *ThirdPartyTTSWorker) callMinimaxTTSService(ctx context.Context, MinimaxRequest MinimaxTTSRequest) (*MinimaxTTSResponse, error) {
	// 添加 restClient 检查
	if w.restClient == nil {
		return nil, fmt.Errorf("rest client is not initialized")
	}
	g.Log().Infof(ctx, "sending Minimax TTS request to URL: %s", w.thirdPartyTTSUrl)

	maxRetryCount := w.retryConfig.MaxRetryCount
	baseDelay := w.retryConfig.BaseDelay
	maxDelay := w.retryConfig.MaxDelay
	// 默认值
	if maxRetryCount <= 0 {
		maxRetryCount = 10 // 默认最大重试次数
	}
	if baseDelay <= 0 {
		baseDelay = 2 // 默认初始延迟时间
	}
	if maxDelay <= 0 {
		maxDelay = 10 // 默认最大延迟时间
	}
	backoffFactor := w.retryConfig.BackoffFactor
	if backoffFactor <= 0 {
		backoffFactor = 2.0 // 默认退避因子
	}

	// 创建响应对象
	MinimaxResponse := new(MinimaxTTSResponse)
	MinimaxErrorResp := new(MinimaxErrorInfo)
	var lastErr error

	// 重试逻辑
	retryCount := 0
	defer func() {
		if retryCount > 0 {
			monitor.RecordThirdPartyApiRetryCount(w.taskType, "minimax_tts", retryCount)
		}
	}()
	for retryCount = 0; retryCount <= maxRetryCount; retryCount++ {
		retryDelay := time.Duration(0)
		if retryCount > 0 {
			// 计算退避延迟时间：baseDelay * (backoffFactor ^ retryCount)
			delay := float64(baseDelay) * math.Pow(backoffFactor, float64(retryCount))
			// 确保不超过最大延迟
			if delay > float64(maxDelay) {
				delay = float64(maxDelay)
			}
			retryDelay = time.Duration(delay) * time.Second

			g.Log().Infof(ctx, "Retrying Minimax TTS request, attempt %d/%d after %v seconds",
				retryCount, maxRetryCount, retryDelay)

			time.Sleep(retryDelay)
		}

		startTime := time.Now()
		// 发送请求
		resp, err := w.restClient.R(ctx).
			SetContext(ctx).
			SetHeader("Content-Type", "application/json").
			SetHeader("Authorization", fmt.Sprintf("Bearer %s", w.thirdPartyAuthKey)).
			SetBody(MinimaxRequest).
			SetResult(MinimaxResponse).
			SetError(MinimaxErrorResp).
			Post(w.thirdPartyTTSUrl)

		duration := time.Since(startTime)
		success := err == nil && resp.StatusCode() == 200

		// 记录API调用情况
		monitor.ObserveThirdPartyApiCall(w.taskType, "minimax_tts", duration, success)
		errInfo := &MinimaxError{}
		errInfo = MinimaxResponse.Error
		if errInfo == nil {
			errInfo = MinimaxErrorResp.Error
		}
		// 如果请求失败，直接返回错误
		if !success {
			if err != nil {
				return nil, fmt.Errorf("Minimax TTS request failed: %w", err)
			}

			g.Log().Infof(ctx, "Minimax TTS request failed with status code: %d", resp.StatusCode())

			// 处理 599 超时错误
			if resp.StatusCode() == 599 {
				monitor.RecordThirdPartyApiRateLimit(w.taskType, "minimax_tts")
				if retryCount < maxRetryCount {
					g.Log().Infof(ctx, "Request timeout (status: 599), will retry after %d seconds", retryDelay)
					lastErr = fmt.Errorf("request timeout")
					continue
				}
				return nil, fmt.Errorf("Minimax TTS service timeout after %d retries", maxRetryCount)
			}

			// 处理业务错误
			if errInfo != nil && errInfo.Code != 0 {
				g.Log().Infof(ctx, "Minimax TTS request failed with status: %d, errorCode: %d, errorMsg:%s",
					resp.StatusCode(), errInfo.Code, errInfo.Message)

				// 处理需要重试的错误码
				if errInfo.Code == 102004 || errInfo.Code == 102005 {
					monitor.RecordThirdPartyApiRateLimit(w.taskType, "minimax_tts")
					if retryCount < maxRetryCount {
						g.Log().Infof(ctx, "Rate limit error (code: %d), will retry after %d seconds",
							errInfo.Code, retryDelay)
						lastErr = fmt.Errorf("rate limit error: %s", errInfo.Message)
						continue
					}

					return nil, fmt.Errorf("Minimax TTS service rate limit exceeded after %d retries: %s",
						maxRetryCount, errInfo.Message)
				}

				// 其他错误码直接返回错误
				return nil, fmt.Errorf("Minimax TTS service error: %s (code=%d)",
					errInfo.Message, errInfo.Code)
			}

			// 处理其他非200状态码错误
			return nil, fmt.Errorf("Minimax TTS service returned non-200 status: %d", resp.StatusCode())
		}

		// 请求成功且没有错误
		if MinimaxResponse != nil && MinimaxResponse.Code == 0 {
			g.Log().Infof(ctx, "Minimax TTS request succeeded, response: %v", MinimaxResponse)
		} else {
			g.Log().Errorf(ctx, "Minimax TTS request failed with unexpected err response: %v", errInfo)
			return nil, fmt.Errorf("unexpected Minimax TTS err response: %v", errInfo)
		}
		return MinimaxResponse, nil
	}

	return nil, lastErr
}

// 验证任务参数
func (w *ThirdPartyTTSWorker) validateTask(ttsRequest ThirdPartyTTSRequest) error {
	switch w.taskType {
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_MINIMAX:
		if ttsRequest.SpeakerName == "" {
			return errors.New("speaker_name cannot be empty")
		}
		return nil
	// 可以添加更多第三方服务类型的case
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_11LABS:
		return nil
	default:
		return errors.New("unsupported third-party type")
	}
}

// 检查URL是否有效
func isValidThirdPartyUrl(rawUrl string) bool {
	_, err := url.ParseRequestURI(rawUrl)
	return err == nil
}

// 下载音频到临时文件
func (w *ThirdPartyTTSWorker) downloadAudioToTempFile(ctx context.Context, audioURL string, useOBS bool) (string, error) {
	g.Log().Infof(ctx, "Downloading audio from URL: %s", audioURL)

	// 创建临时文件
	tempFile, err := os.CreateTemp("", "minimax_tts_*.mp3")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %w", err)
	}
	tempFilePath := tempFile.Name()
	tempFile.Close()

	if useOBS {
		// 确保OBS客户端已初始化
		obsClient := obs.GetOBSClient()
		if obsClient == nil {
			// 初始化OBS客户端
			obs.InitOBSClient()
			obsClient = obs.GetOBSClient()
			if obsClient == nil {
				return "", fmt.Errorf("failed to initialize OBS client")
			}
		}
		err = obsClient.DownloadFile(audioURL, tempFilePath)
		if err != nil {
			return "", fmt.Errorf("failed to download audio file: %w", err)
		}

	} else {
		// 下载文件
		resp, err := w.restClient.R(ctx).
			SetContext(ctx).
			SetOutput(tempFilePath).
			Get(audioURL)
		if err != nil {
			os.Remove(tempFilePath) // 清理临时文件
			return "", fmt.Errorf("failed to download audio file: %w", err)
		}

		if resp.StatusCode() != 200 {
			os.Remove(tempFilePath) // 清理临时文件
			return "", fmt.Errorf("failed to download audio file, status code: %d", resp.StatusCode())
		}
	}
	g.Log().Infof(ctx, "Audio file downloaded to: %s", tempFilePath)
	return tempFilePath, nil
}

// 上传到OBS
func (w *ThirdPartyTTSWorker) uploadToOBS(ctx context.Context, localFilePath string, objectKey string) (string, error) {
	g.Log().Infof(ctx, "Uploading audio file to OBS, path: %s, key: %s", localFilePath, objectKey)

	// 确保OBS客户端已初始化
	obsClient := obs.GetOBSClient()
	if obsClient == nil {
		// 初始化OBS客户端
		obs.InitOBSClient()
		obsClient = obs.GetOBSClient()
		if obsClient == nil {
			return "", fmt.Errorf("failed to initialize OBS client")
		}
	}
	// 上传文件到OBS
	url, err := obs.UploadFile(localFilePath, objectKey)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to OBS: %w", err)
	}

	g.Log().Infof(ctx, "Audio file uploaded to OBS successfully, URL: %s", url)
	return url, nil
}
