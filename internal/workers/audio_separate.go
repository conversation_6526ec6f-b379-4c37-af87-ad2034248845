package workers

import (
	"context"
	"errors"
	"fmt"
	"time"

	"omni_worker/internal/util"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type AudioSeparateRequest struct {
	UserID                int64                 `json:"user_id"`
	TaskID                int64                 `json:"task_id"`
	AudioUrl              string                `json:"audio_url"`
	EnableASR             bool                  `json:"enable_asr"`
	SourceLang            string                `json:"source_lang"`
	PreprocResultCb       string                `json:"preproc_result_cb"`
	SourceText            string                `json:"source_text"`
	UserTranslatedSrtData []*omniEngine.SrtItem `json:"user_translated_srt_data"`
	AsrInfos              *omniEngine.AsrInfo   `json:"asr_infos"`      // 火山云asr识别结果
	SpeakerNumber         int32                 `json:"speaker_number"` // 说话人数量
}

type AudioSeparateResponse struct {
	Code    int    `json:"code"` // 0:成功
	Message string `json:"message"`
}

// 1: 完成，2: 失败，3: 正在处理，4: 任务ID不正确
const (
	AudioSeparateStatusSuccess       = 1
	AudioSeparateStatusFailed        = 2
	AudioSeparateStatusRunning       = 3
	AudioSeparateStatusInvalidTaskID = 4
)

type AudioSeparateWorker struct {
	*BaseWorker
	audioSeparateCreateUrl string
	audioSeparateResultUrl string
	checkResultInterval    int // 检查结果的时间间隔（毫秒）
	taskType               omniEngine.TaskType
}

type VocalInfo struct {
	VocalID      string     `json:"vocal_id"`
	VocalUrl     string     `json:"vocal_url"`
	Vocal16KUrl  string     `json:"vocal_16k_url"`
	Start        float64    `json:"start"`
	End          float64    `json:"end"`
	Speaker      string     `json:"speaker"`
	Text         string     `json:"text"` // asr识别结果
	Language     string     `json:"language"`
	Duration     float64    `json:"duration"`
	MatchingRate float64    `json:"matching_rate"`
	Score        float64    `json:"score"` // 评分
	Words        []WordInfo `json:"words"` // 词信息
	Label        string     `json:"label"` // 标签
}

type GetAudioSeparateData struct {
	BgUrl            string         `json:"bg_url"`
	FullVocalUrl     string         `json:"full_vocal_url"`
	VocalInfos       []VocalInfo    `json:"vocal_infos"`
	DiaInfos         []DiaInfo      `json:"dia_infos"`
	SpeakerAudioList []SpeakerAudio `json:"speaker_audio_list,omitempty"` // 说话人语音列表
}

type GetAudioSeparateResponse struct {
	UserID  int                  `json:"user_id"`
	TaskID  int64                `json:"task_id"`
	Status  int                  `json:"status"` // 1: 完成，2: 失败，3: 正在处理，4: 任务ID不正确
	Message string               `json:"message"`
	Data    GetAudioSeparateData `json:"data"`
}

func NewAudioSeparateWorker(taskType omniEngine.TaskType) *AudioSeparateWorker {
	cfg := config.GetConfig()
	checkResultInterval := cfg.AudioSeparateService.CheckResultIntervalMs
	if checkResultInterval == 0 {
		checkResultInterval = 300
	}
	return &AudioSeparateWorker{
		BaseWorker:             NewBaseWorker(),
		audioSeparateCreateUrl: cfg.AudioSeparateService.CreateUrl,
		audioSeparateResultUrl: cfg.AudioSeparateService.ResultUrl,
		checkResultInterval:    checkResultInterval,
		taskType:               taskType,
	}
}

func (w *AudioSeparateWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *AudioSeparateWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (a *AudioSeparateWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting Audio Separate task: %d", t.Id)

	audioSeparateRequest := AudioSeparateRequest{
		UserID:                t.TenantId,
		TaskID:                t.Id,
		AudioUrl:              t.AudioSeparateTaskInputContent.AudioUrl,
		EnableASR:             t.AudioSeparateTaskInputContent.EnableAsr,
		SourceLang:            t.AudioSeparateTaskInputContent.SourceLang,
		SourceText:            t.AudioSeparateTaskInputContent.SourceText,
		UserTranslatedSrtData: t.AudioSeparateTaskInputContent.UserTranslatedSrtData,
		SpeakerNumber:         t.AudioSeparateTaskInputContent.SpeakerNumber,
	}
	if audioSeparateRequest.UserTranslatedSrtData == nil { // 避免 上游没传这个字段时, 默认赋值为nil,传给算法导致报错
		audioSeparateRequest.UserTranslatedSrtData = []*omniEngine.SrtItem{}
	}
	if t.GetAudioSeparateTaskInputContent().GetAsrInfo() != nil {
		audioSeparateRequest.AsrInfos = t.GetAudioSeparateTaskInputContent().GetAsrInfo()
	}
	return a.handleAudioSeparateTask(ctx, audioSeparateRequest)
}

func (a *AudioSeparateWorker) handleAudioSeparateTask(ctx context.Context, request AudioSeparateRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call audio separate request: %+v", util.ToJson(request))
	if err := a.validateTask(request); err != nil {
		g.Log().Errorf(ctx, "Invalid audio separate task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid audio separate task: %v", err),
		}
	}

	var response AudioSeparateResponse
	err := retry.Do(func() error {
		return a.PostJSON(ctx, a.audioSeparateCreateUrl, request, &response)
	},
		retry.Attempts(3),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "audio separate task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("audio separate task failed: %v", err),
		}
	}
	g.Log().Infof(ctx, "Call audio separate response: %+v", util.ToJson(response))

	return a.handleTaskResponse(ctx, request, &response)
}

func (a *AudioSeparateWorker) validateTask(request AudioSeparateRequest) error {
	if !isValidUrl(request.AudioUrl) {
		return errors.New("invalid audio_url: must be a valid HTTP/HTTPS Url")
	}

	return nil
}

func (a *AudioSeparateWorker) handleTaskResponse(ctx context.Context, request AudioSeparateRequest, response *AudioSeparateResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 0：任务发送成功，要进行检测
	switch response.Code {
	case Success:
		g.Log().Infof(ctx, "Task %d is still processing, checking status later", request.TaskID)
		return a.checkTaskStatus(ctx, request)
	default:
		errorMsg := fmt.Sprintf("Task %d failed with code: %v", request.TaskID, response.Code)
		g.Log().Errorf(ctx, errorMsg)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: StatusFailed,
		}
	}
}

func (a *AudioSeparateWorker) checkTaskStatus(ctx context.Context, request AudioSeparateRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	taskId := request.TaskID
	statusRequest := map[string]interface{}{
		"user_id": request.UserID,
		"task_id": taskId,
	}
	timeout := time.NewTimer(1 * time.Hour)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			return a.handleFailedStatus(ctx, &GetAudioSeparateResponse{})
		default:
			var response GetAudioSeparateResponse
			err := retry.Do(func() error {
				return a.PostJSON(ctx, a.audioSeparateResultUrl, statusRequest, &response)
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "Failed to call audio separate status check service: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Failed to call audio separate status check service: %v", err),
				}
			}
			g.Log().Infof(ctx, "Get preproc request: %+v , response: %+v", util.ToJson(request), util.ToJson(response))
			// 1: 完成，2: 失败，3: 正在处理，4: 任务ID不正确
			switch response.Status {
			case AudioSeparateStatusSuccess:
				return a.handleCompletedStatus(ctx, &response)
			case AudioSeparateStatusRunning:
				g.Log().Infof(ctx, "Audio separate task %d is still processing", taskId)
				time.Sleep(time.Duration(a.checkResultInterval) * time.Millisecond)
			default:
				return a.handleFailedStatus(ctx, &response)
			}
		}
	}
}

func (a *AudioSeparateWorker) handleCompletedStatus(ctx context.Context, response *GetAudioSeparateResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Audio separate task %d completed", response.TaskID)
	taskOutputContent := &TaskOutputContent{
		Code:    CodeCompleted,
		Message: StatusCompleted,
		Data:    response.Data,
	}
	return omniEngine.TaskStatus_COMPLETED, taskOutputContent
}

func (a *AudioSeparateWorker) handleFailedStatus(ctx context.Context, response *GetAudioSeparateResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	errorMsg := fmt.Sprintf("Audio separate task %d failed with code %d, message: %s", response.TaskID, response.Status, response.Message)
	g.Log().Errorf(ctx, errorMsg)
	taskOutputContent := &TaskOutputContent{
		Code:    CodeTaskFailed,
		Message: errorMsg,
	}
	return omniEngine.TaskStatus_FAILED, taskOutputContent
}
