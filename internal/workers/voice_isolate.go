package workers

import (
	"context"
	"errors"
	"fmt"
	"time"

	"omni_worker/pkg/config"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type VoiceIsolateRequest struct {
	UserID          int64  `json:"user_id"`
	TaskID          int64  `json:"task_id"`
	AudioUrl        string `json:"audio_url"`
	PreprocResultCb string `json:"preproc_result_cb"`
}

type VoiceIsolateResponse struct {
	Code    int    `json:"code"` // 0:成功
	Message string `json:"message"`
}

type VoiceIsolateWorker struct {
	*BaseWorker
	VoiceIsolateCreateUrl string
	VoiceIsolateResultUrl string
	checkResultInterval   int // 检查结果的时间间隔（毫秒）
	taskType              omniEngine.TaskType
}

type GetVoiceIsolateData struct {
	BgUrl        string `json:"bg_url"`
	FullVocalUrl string `json:"full_vocal_url"`
}

type GetVoiceIsolateResponse struct {
	UserID  int                 `json:"user_id"`
	TaskID  int64               `json:"task_id"`
	Status  int                 `json:"status"` // 1: 完成，2: 失败，3: 正在处理，4: 任务ID不正确
	Message string              `json:"message"`
	Data    GetVoiceIsolateData `json:"data"`
}

// 完成，2: 失败，3: 正在处理，4: 任务ID不正确
const (
	VoiceIsolateStatusCompleted     = 1
	VoiceIsolateStatusFailed        = 2
	VoiceIsolateStatusProcessing    = 3
	VoiceIsolateStatusInvalidTaskID = 4
)

func NewVoiceIsolateWorker(taskType omniEngine.TaskType) *VoiceIsolateWorker {
	cfg := config.GetConfig()
	checkResultInterval := cfg.VoiceIsolateService.CheckResultIntervalMs
	if checkResultInterval == 0 {
		checkResultInterval = 300
	}
	return &VoiceIsolateWorker{
		BaseWorker:            NewBaseWorker(),
		VoiceIsolateCreateUrl: cfg.VoiceIsolateService.CreateUrl,
		VoiceIsolateResultUrl: cfg.VoiceIsolateService.ResultUrl,
		checkResultInterval:   checkResultInterval,
		taskType:              taskType,
	}
}

func (w *VoiceIsolateWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *VoiceIsolateWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (a *VoiceIsolateWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting voice isolate task: %d", t.Id)

	voiceIsolateRequest := VoiceIsolateRequest{
		UserID:   t.TenantId,
		TaskID:   t.Id,
		AudioUrl: t.VoiceIsolateTaskInputContent.AudioUrl,
	}
	return a.handleVoiceIsolateTask(ctx, voiceIsolateRequest)
}

func (a *VoiceIsolateWorker) handleVoiceIsolateTask(ctx context.Context, request VoiceIsolateRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call voice isolate request: %+v", request)
	if err := a.validateTask(request); err != nil {
		g.Log().Errorf(ctx, "Invalid voice isolate task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid voice isolate task: %v", err),
		}
	}

	var response VoiceIsolateResponse
	if err := a.PostJSON(ctx, a.VoiceIsolateCreateUrl, request, &response); err != nil {
		g.Log().Errorf(ctx, "voice isolate task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("voice isolate task failed: %v", err),
		}
	}
	g.Log().Infof(ctx, "Call voice isolate response: %+v", response)

	return a.handleTaskResponse(ctx, request, &response)
}

func (a *VoiceIsolateWorker) validateTask(request VoiceIsolateRequest) error {
	if !isValidUrl(request.AudioUrl) {
		return errors.New("invalid audio_url: must be a valid HTTP/HTTPS Url")
	}

	return nil
}

func (a *VoiceIsolateWorker) handleTaskResponse(ctx context.Context, request VoiceIsolateRequest, response *VoiceIsolateResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 0：任务发送成功，要进行检测
	switch response.Code {
	case Success:
		g.Log().Infof(ctx, "Task %d is still processing, checking status later", request.TaskID)
		return a.checkTaskStatus(ctx, request)
	default:
		errorMsg := fmt.Sprintf("Task %d failed with code: %v", request.TaskID, response.Code)
		g.Log().Errorf(ctx, errorMsg)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: StatusFailed,
		}
	}
}

func (a *VoiceIsolateWorker) checkTaskStatus(ctx context.Context, request VoiceIsolateRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	taskId := request.TaskID
	statusRequest := map[string]interface{}{
		"user_id": request.UserID,
		"task_id": taskId,
	}

	timeout := time.NewTimer(time.Duration(15) * time.Minute)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "Timeout waiting for voice isolate task %d to complete", taskId)
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: fmt.Sprintf("Timeout waiting for voice isolate task %d to complete", taskId),
			}
		default:
			var response GetAudioSeparateResponse
			if err := a.PostJSON(ctx, a.VoiceIsolateResultUrl, statusRequest, &response); err != nil {
				g.Log().Errorf(ctx, "Failed to call voice isolate status check service: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Failed to call voice isolate status check service: %v", err),
				}
			}
			g.Log().Infof(ctx, "Get preproc request: %+v , response: %+v", request, response)

			// 1: 完成，2: 失败，3: 正在处理，4: 任务ID不正确
			switch response.Status {
			case VoiceIsolateStatusCompleted:
				return a.handleCompletedStatus(ctx, &response)
			case VoiceIsolateStatusProcessing:
				g.Log().Infof(ctx, "voice isolate task %d is still processing", taskId)
				time.Sleep(time.Duration(a.checkResultInterval) * time.Millisecond)
			default:
				return a.handleFailedStatus(ctx, &response)
			}
		}
	}
}

func (a *VoiceIsolateWorker) handleCompletedStatus(ctx context.Context, response *GetAudioSeparateResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "voice isolate task %d completed", response.TaskID)
	taskOutputContent := &TaskOutputContent{
		Code:    CodeCompleted,
		Message: StatusCompleted,
		Data:    response.Data,
	}
	return omniEngine.TaskStatus_COMPLETED, taskOutputContent
}

func (a *VoiceIsolateWorker) handleFailedStatus(ctx context.Context, response *GetAudioSeparateResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	errorMsg := fmt.Sprintf("voice isolate task %d failed with code %d, message: %s", response.TaskID, response.Status, response.Message)
	g.Log().Errorf(ctx, errorMsg)
	taskOutputContent := &TaskOutputContent{
		Code:    CodeTaskFailed,
		Message: errorMsg,
	}
	return omniEngine.TaskStatus_FAILED, taskOutputContent
}
