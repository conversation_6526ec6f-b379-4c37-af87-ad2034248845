package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"sync"
	"time"

	"omni_worker/pkg/config"
	"omni_worker/pkg/media"
	"omni_worker/pkg/obs"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	jsoniter "github.com/json-iterator/go"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

const (
	onlyTranscodingMode     = 0
	transcodingAndMergeMode = 1
)

// map 0:ultrafast, 1:slow，2：medium，3：fast
var presetMap = map[int]string{
	0: "ultrafast",
	1: "slow",
	2: "medium",
	3: "fast",
}

type VideoTranscodingRequest struct {
	TaskID      int64         `json:"task_id"`
	VideoParams []*VideoParam `json:"video_params"` // 视频信息
}

type VideoInfo struct {
	ID             int64  `json:"id"`
	OriginVideoUrl string `json:"origin_video_url"`
	ResultVideoUrl string `json:"result_video_url"`
}

type VideoParam struct {
	VideoInfos    []*VideoInfo `json:"video_infos"`     // 视频信息
	FPS           int          `json:"fps"`             // 默认30
	CRF           int          `json:"crf"`             // 控制视频质量（范围0-51，值越小质量越高），默认28
	Resolution    string       `json:"resolution"`      // 分辨率1280x720
	Preset        int          `json:"preset"`          // 编码预设，默认0:ultrafast, 1:slow，2：medium，3：fast
	Mode          int          `json:"mode"`            // 模式，0:默认只转码，1: 转码并合并视频
	MergeVideoUrl string       `json:"merge_video_url"` // 合成视频url，mode=1时必填
}

type VideoTranscodingResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type videoTranscodingBO struct {
	ID                  int64
	OriginVideoUrl      string
	ResultVideoUrl      string
	FPS                 int
	CRF                 int
	Preset              string
	Resolution          string
	downloadFilePath    string
	transcodingFilePath string
}

type VideoTranscodingWorker struct {
	*BaseWorker
	taskType omniEngine.TaskType
}

func NewVideoTranscodingWorker(taskType omniEngine.TaskType) *VideoTranscodingWorker {
	return &VideoTranscodingWorker{
		BaseWorker: NewBaseWorker(),
		taskType:   taskType,
	}
}

func (w *VideoTranscodingWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *VideoTranscodingWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *VideoTranscodingWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	request := &VideoTranscodingRequest{}
	err := json.Unmarshal([]byte(t.CommonInputContent), request)
	if err != nil {
		g.Log().Errorf(ctx, "[Process] unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	request.TaskID = t.Id
	return w.handleVideoTranscodingTask(ctx, request)
}

func (w *VideoTranscodingWorker) handleVideoTranscodingTask(ctx context.Context, req *VideoTranscodingRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	reqStr, _ := jsoniter.MarshalToString(req)
	g.Log().Infof(ctx, "[handleVideoTranscodingTask] start videoTranscoding, request: %s", reqStr)
	// 校验参数
	err := w.validateVideoParams(req.VideoParams)
	if err != nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("validate video params failed: %v", err),
		}
	}
	downloadVideoStartTime := time.Now()
	// 并发下载视频
	videoTranscodingBOMap, videoId2DownloadPath, err := w.downloadVideo(ctx, req.TaskID, req.VideoParams)
	defer func() {
		// 清理临时文件
		for _, videoPath := range videoId2DownloadPath {
			os.Remove(videoPath)
		}
	}()
	if err != nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "download video failed",
		}
	}
	g.Log().Infof(ctx, "taskID:%d, download video success, cost: %.2fs", req.TaskID, time.Since(downloadVideoStartTime).Seconds())
	// 校验视频
	err = w.validateVideo(ctx, videoTranscodingBOMap)
	if err != nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: "invalid video file",
		}
	}
	// 根据模式选择处理方式
	processingStartTime := time.Now()
	if req.VideoParams[0].Mode == onlyTranscodingMode {
		// 只转码模式：先转码再上传
		err = w.transcoding(ctx, req.TaskID, videoTranscodingBOMap)
		if err != nil {
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: "video transcoding failed",
			}
		}
		g.Log().Infof(ctx, "taskID:%d, transcoding success, cost: %.2fs", req.TaskID, time.Since(processingStartTime).Seconds())
		
		// 上传转码后的视频
		uploadStartTime := time.Now()
		err = w.uploadTranscodedVideos(ctx, req.TaskID, req.VideoParams, videoTranscodingBOMap)
		if err != nil {
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: "video upload failed",
			}
		}
		g.Log().Infof(ctx, "taskID:%d, upload success, cost: %.2fs", req.TaskID, time.Since(uploadStartTime).Seconds())
	} else {
		// 合成模式：直接合成并上传（跳过单独转码步骤）
		err = w.mergeAndUploadOptimized(ctx, req.TaskID, req.VideoParams, videoTranscodingBOMap)
		if err != nil {
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: "video merge and upload failed",
			}
		}
		g.Log().Infof(ctx, "taskID:%d, merge and upload success, cost: %.2fs", req.TaskID, time.Since(processingStartTime).Seconds())
	}
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    CodeSuccess,
		Message: "success",
	}
}

func (w *VideoTranscodingWorker) validateVideoParams(videoParams []*VideoParam) error {
	if len(videoParams) == 0 {
		return fmt.Errorf("video params is empty")
	}
	
	// 校验所有VideoParam的mode必须一致
	firstMode := videoParams[0].Mode
	for i, videoParam := range videoParams {
		// 校验分辨率格式「1280x720」
		if !regexp.MustCompile(`^\d+x\d+$`).MatchString(videoParam.Resolution) {
			return fmt.Errorf("invalid resolution format: %s", videoParam.Resolution)
		}
		// 校验crf范围
		if videoParam.CRF < 0 || videoParam.CRF > 51 {
			return fmt.Errorf("invalid crf value: %d", videoParam.CRF)
		}
		// 校验模式
		if videoParam.Mode != onlyTranscodingMode && videoParam.Mode != transcodingAndMergeMode {
			return fmt.Errorf("invalid mode: %d", videoParam.Mode)
		}
		// 校验mode一致性
		if videoParam.Mode != firstMode {
			return fmt.Errorf("all video params must have the same mode, found mode %d at index %d, expected %d", videoParam.Mode, i, firstMode)
		}
		// 校验合并视频url
		if videoParam.Mode == transcodingAndMergeMode && videoParam.MergeVideoUrl == "" {
			return fmt.Errorf("merge video url is required")
		}
		// 校验preset
		if _, exists := presetMap[videoParam.Preset]; !exists {
			return fmt.Errorf("invalid preset: %d", videoParam.Preset)
		}
	}
	return nil
}

func (w *VideoTranscodingWorker) validateVideo(ctx context.Context, videoTranscodingBOMap map[string]*videoTranscodingBO) error {
	wg := concurrent.New(ctx, 6)
	for _, tmpVideoTranscodingBO := range videoTranscodingBOMap {
		videoTranscodingBO := tmpVideoTranscodingBO
		wg.Go(func() error {
			return media.VideoValidate(ctx, videoTranscodingBO.downloadFilePath)
		})
	}
	return wg.Wait()
}

func (w *VideoTranscodingWorker) downloadVideo(ctx context.Context, taskID int64, videoParams []*VideoParam) (map[string]*videoTranscodingBO, map[int64]string, error) {
	// 视频根据id去重
	originUrlMap := make(map[int64]string)
	for _, videoParam := range videoParams {
		for _, videoInfo := range videoParam.VideoInfos {
			originUrlMap[videoInfo.ID] = videoInfo.OriginVideoUrl
		}
	}
	// 并发下载视频
	timeOutCtx, cancel := context.WithTimeout(ctx, 15*time.Minute)
	defer cancel()
	wg := concurrent.New(timeOutCtx, 6)
	lock := sync.Mutex{}
	downloadPathMap := make(map[int64]string)
	for tmpVideoID, tmpOriginUrl := range originUrlMap {
		videoID := tmpVideoID
		originUrl := tmpOriginUrl
		wg.Go(func() error {
			return retry.Do(func() error {
				tempFilePath, err := w.downloadVideoToTempFile(ctx, originUrl)
				if err != nil {
					g.Log().Errorf(ctx, "[downloadVideo] taskID:%d, download video failed: %v", taskID, err)
					return err
				}
				lock.Lock()
				defer lock.Unlock()
				downloadPathMap[videoID] = tempFilePath
				return nil
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
		})
	}
	err := wg.Wait()
	if err != nil {
		// 部分文件下载成功，需要清理
		g.Log().Errorf(ctx, "[downloadVideo] taskID:%d, download video failed: %v", taskID, err)
		return nil, downloadPathMap, err
	}
	// 构建去重bo
	videoTranscodingBOMap := make(map[string]*videoTranscodingBO)
	for _, videoParam := range videoParams {
		for _, videoInfo := range videoParam.VideoInfos {
			key := genTranscodingKey(videoInfo.ID, videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
			if _, ok := videoTranscodingBOMap[key]; ok {
				continue
			}
			downloadPath, ok := downloadPathMap[videoInfo.ID]
			if !ok {
				g.Log().Errorf(ctx, "[downloadVideo] taskID:%d, download file path not found, videoId: %d", taskID, videoInfo.ID)
				return nil, downloadPathMap, err
			}
			videoTranscodingBOMap[key] = &videoTranscodingBO{
				ID:               videoInfo.ID,
				OriginVideoUrl:   videoInfo.OriginVideoUrl,
				ResultVideoUrl:   videoInfo.ResultVideoUrl,
				Resolution:       videoParam.Resolution,
				CRF:              videoParam.CRF,
				FPS:              videoParam.FPS,
				Preset:           presetMap[videoParam.Preset],
				downloadFilePath: downloadPath,
			}
		}
	}
	return videoTranscodingBOMap, downloadPathMap, nil
}

// 并发转码
func (w *VideoTranscodingWorker) transcoding(ctx context.Context, taskID int64, videoTranscodingBOMap map[string]*videoTranscodingBO) error {
	timeOutCtx, cancel := context.WithTimeout(ctx, 15*time.Minute)
	defer cancel()
	wg := concurrent.New(timeOutCtx, 4)
	for _, tmpVideoTranscodingBO := range videoTranscodingBOMap {
		bo := tmpVideoTranscodingBO
		wg.Go(func() error {
			tempFile, err := os.CreateTemp("", "video_transcoding_*.mp4")
			if err != nil {
				return fmt.Errorf("taskID:%d, failed to create temp file: %w", taskID, err)
			}
			tempFilePath := tempFile.Name()
			tempFile.Close()
			bo.transcodingFilePath = tempFilePath
			// 视频转码，假如视频没有音频流，添加静音音频
			err = media.VideoTranscoding(ctx, bo.downloadFilePath, bo.transcodingFilePath, bo.Resolution, bo.Preset, bo.CRF, bo.FPS)
			if err != nil {
				g.Log().Errorf(ctx, "[handleVideoTranscodingTask] taskID:%d, video transcoding failed: %v", taskID, err)
				return err
			}
			return nil
		})
	}
	err := wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "[transcoding] taskID:%d, transcoding failed: %v", taskID, err)
		return err
	}
	return nil
}

// 上传转码后的视频（仅转码模式）
func (w *VideoTranscodingWorker) uploadTranscodedVideos(ctx context.Context, taskID int64, videoParams []*VideoParam, videoTranscodingBOMap map[string]*videoTranscodingBO) error {
	timeOutCtx, cancel := context.WithTimeout(ctx, 15*time.Minute)
	defer cancel()
	wg := concurrent.New(timeOutCtx, 6)
	
	// 清理临时文件
	defer func() {
		for _, bo := range videoTranscodingBOMap {
			if bo.transcodingFilePath != "" {
				os.Remove(bo.transcodingFilePath)
			}
		}
	}()
	
	for _, videoParam := range videoParams {
		for _, videoInfo := range videoParam.VideoInfos {
			key := genTranscodingKey(videoInfo.ID, videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
			bo := videoTranscodingBOMap[key]
			if bo == nil || bo.transcodingFilePath == "" {
				return fmt.Errorf("transcoding video not found, key: %s", key)
			}
			
			wg.Go(func() error {
				return retry.Do(func() error {
					_, err := w.uploadToOBS(ctx, bo.transcodingFilePath, videoInfo.ResultVideoUrl)
					if err != nil {
						g.Log().Errorf(ctx, "Failed to upload video to OBS: %s", err.Error())
						return err
					}
					return nil
				},
					retry.Attempts(3),
					retry.Delay(time.Second),
					retry.MaxDelay(30*time.Second),
					retry.DelayType(retry.BackOffDelay),
				)
			})
		}
	}
	
	err := wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload videos to OBS: %s", err.Error())
		return err
	}
	return nil
}

// 优化的合并和上传（合成模式）- 直接合成时转码，跳过单独转码步骤
func (w *VideoTranscodingWorker) mergeAndUploadOptimized(ctx context.Context, taskID int64, videoParams []*VideoParam, videoTranscodingBOMap map[string]*videoTranscodingBO) error {
	timeOutCtx, cancel := context.WithTimeout(ctx, 15*time.Minute)
	defer cancel()
	wg := concurrent.New(timeOutCtx, int64(len(videoParams)))
	lock := sync.Mutex{}
	
	// 存储最终要上传的文件路径
	finalFilePathMap := make(map[string]string)
	
	// 清理临时文件
	defer func() {
		for _, filePath := range finalFilePathMap {
			os.Remove(filePath)
		}
	}()
	
	for idx, videoParam := range videoParams {
		// 收集需要合成的原始视频文件路径
		originalFileList := make([]string, 0, len(videoParam.VideoInfos))
		for _, videoInfo := range videoParam.VideoInfos {
			key := genTranscodingKey(videoInfo.ID, videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
			bo := videoTranscodingBOMap[key]
			if bo == nil {
				return fmt.Errorf("video not found, key: %s", key)
			}
			originalFileList = append(originalFileList, bo.downloadFilePath)
		}
		
		if len(originalFileList) == 0 {
			continue
		}
		
		// 生成合成视频的key
		mergeKey := genTranscodingKey(int64(idx), videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
		
		// 只有一个视频则直接转码，不需要合成
		if len(originalFileList) == 1 {
			wg.Go(func() error {
				tempFile, err := os.CreateTemp("", "video_transcoding_*.mp4")
				if err != nil {
					return fmt.Errorf("taskID:%d, failed to create temp file: %w", taskID, err)
				}
				tempFilePath := tempFile.Name()
				tempFile.Close()
				
				// 直接转码单个视频
				err = media.VideoTranscoding(ctx, originalFileList[0], tempFilePath, videoParam.Resolution, presetMap[videoParam.Preset], videoParam.CRF, videoParam.FPS)
				if err != nil {
					g.Log().Errorf(ctx, "[mergeAndUploadOptimized] taskID:%d, video transcoding failed: %v", taskID, err)
					return err
				}
				
				lock.Lock()
				finalFilePathMap[mergeKey] = tempFilePath
				lock.Unlock()
				return nil
			})
		} else {
			// 多个视频需要合成
			wg.Go(func() error {
				tempFile, err := os.CreateTemp("", "video_merge_*.mp4")
				if err != nil {
					g.Log().Errorf(ctx, "[mergeAndUploadOptimized] taskID:%d, failed to create temp file: %v", taskID, err)
					return err
				}
				tempFilePath := tempFile.Name()
				tempFile.Close()
				
				// 直接合成并转码（一步完成）
				err = media.MergeAndTranscodeVideos(ctx, originalFileList, tempFilePath, videoParam.Resolution, presetMap[videoParam.Preset], videoParam.CRF, videoParam.FPS)
				if err != nil {
					g.Log().Errorf(ctx, "[mergeAndUploadOptimized] taskID:%d, merge and transcode failed: %v", taskID, err)
					return err
				}
				
				// 尝试音量归一化
				normalizedTempFile, err := os.CreateTemp("", "video_normalized_*.mp4")
				if err != nil {
					g.Log().Warning(ctx, "[mergeAndUploadOptimized] taskID:%d, failed to create normalized temp file: %v", taskID, err)
				} else {
					normalizedTempFilePath := normalizedTempFile.Name()
					normalizedTempFile.Close()
					
					// 尝试音量归一化
					err = media.NormalizeAudio(ctx, tempFilePath, normalizedTempFilePath)
					if err != nil {
						g.Log().Warning(ctx, "[mergeAndUploadOptimized] taskID:%d, audio normalization failed, using original: %v", taskID, err)
						os.Remove(normalizedTempFilePath) // 清理失败的文件
					} else {
						// 归一化成功，使用归一化后的文件
						os.Remove(tempFilePath) // 清理原文件
						tempFilePath = normalizedTempFilePath
						g.Log().Info(ctx, "[mergeAndUploadOptimized] taskID:%d, audio normalization success", taskID)
					}
				}
				
				lock.Lock()
				finalFilePathMap[mergeKey] = tempFilePath
				lock.Unlock()
				return nil
			})
		}
	}
	
	err := wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "[mergeAndUploadOptimized] video processing failed: %v", err)
		return err
	}
	
	// 并发上传处理后的视频
	wg = concurrent.New(ctx, 6)
	for idx, videoParam := range videoParams {
		mergeKey := genTranscodingKey(int64(idx), videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
		filePath := finalFilePathMap[mergeKey]
		if filePath == "" {
			return fmt.Errorf("processed video not found, key: %s", mergeKey)
		}
		
		wg.Go(func() error {
			return retry.Do(func() error {
				_, err := w.uploadToOBS(ctx, filePath, videoParam.MergeVideoUrl)
				if err != nil {
					g.Log().Errorf(ctx, "Failed to upload merged video to OBS: %s", err.Error())
					return err
				}
				return nil
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay),
			)
		})
	}
	
	err = wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload merged videos to OBS: %s", err.Error())
		return err
	}
	return nil
}

// 原有的合并和上传方法（保留用于兼容性）
func (w *VideoTranscodingWorker) mergeAndUpload(ctx context.Context, taskID int64, videoParams []*VideoParam, videoTranscodingBOMap map[string]*videoTranscodingBO) error {
	// 构建 video_id -> transcodingFilePath
	timeOutCtx, cancel := context.WithTimeout(ctx, 15*time.Minute)
	defer cancel()
	wg := concurrent.New(timeOutCtx, int64(len(videoParams)))
	lock := sync.Mutex{}
	// 获取需要上传的转码视频地址
	transcodingFilePathMap := make(map[string]string)
	for idx, videoParam := range videoParams {
		// 不用合并的视频，直接上传
		mergeFileList := make([]string, 0, len(videoParam.VideoInfos))
		for _, videoInfo := range videoParam.VideoInfos {
			key := genTranscodingKey(videoInfo.ID, videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
			bo := videoTranscodingBOMap[key]
			if bo == nil {
				return fmt.Errorf("transcoding video not found,  key: %s", key)
			}
			mergeFileList = append(mergeFileList, bo.transcodingFilePath)
			lock.Lock()
			transcodingFilePathMap[key] = bo.transcodingFilePath
			lock.Unlock()
		}
		if len(mergeFileList) == 0 {
			continue
		}
		// 需要合并的视频
		if videoParam.Mode == transcodingAndMergeMode {
			// 特殊处理需要合并的视频，用idx作为video_id
			key := genTranscodingKey(int64(idx), videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
			// 只有一个视频则不用合成
			if len(mergeFileList) == 1 {
				lock.Lock()
				transcodingFilePathMap[key] = mergeFileList[0]
				lock.Unlock()
				continue
			}
			// 按照传入顺序合成视频
			wg.Go(func() error {
				tempFile, err := os.CreateTemp("", "video_transcoding_*.mp4")
				if err != nil {
					g.Log().Errorf(ctx, "[uploadAndMerge] taskID:%d, failed to create temp file: %v", taskID, err)
					return err
				}
				tempFilePath := tempFile.Name()
				tempFile.Close()
				// 合并并归一化音频
				err = media.MergeVideosWithNormalization(ctx, mergeFileList, tempFilePath)
				if err != nil {
					g.Log().Warning(ctx, "[uploadAndMerge] taskID:%d, merge video and normalization failed: %v", taskID, err)
					// 兜底合并视频，不归一化音频
					err = media.MergeVideos(ctx, mergeFileList, tempFilePath)
					if err != nil {
						g.Log().Errorf(ctx, "[uploadAndMerge] taskID:%d, merge video failed: %v", taskID, err)
						return err
					}
				}
				lock.Lock()
				defer lock.Unlock()
				transcodingFilePathMap[key] = tempFilePath
				return nil
			})
		}
	}
	// 清理临时文件
	defer func() {
		for _, filePath := range transcodingFilePathMap {
			os.Remove(filePath)
		}
	}()
	err := wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "[uploadAndMerge] video merge failed: %v", err)
		return err
	}
	// 并发上传视频
	wg = concurrent.New(ctx, 6)
	type uploadParam struct {
		transcodingFilePath string
		resultVideoUrl      string
	}
	uploadParams := make([]*uploadParam, 0)
	for idx, videoParam := range videoParams {
		if videoParam.Mode == transcodingAndMergeMode {
			key := genTranscodingKey(int64(idx), videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
			if transcodingFilePathMap[key] == "" {
				return fmt.Errorf("transcoding video not found,  key: %s", key)
			}
			uploadParams = append(uploadParams, &uploadParam{
				transcodingFilePath: transcodingFilePathMap[key],
				resultVideoUrl:      videoParam.MergeVideoUrl,
			})
		} else {
			for _, videoInfo := range videoParam.VideoInfos {
				key := genTranscodingKey(videoInfo.ID, videoParam.Resolution, videoParam.CRF, videoParam.FPS, videoParam.Preset)
				if transcodingFilePathMap[key] == "" {
					return fmt.Errorf("transcoding video not found,  key: %s", key)
				}
				uploadParams = append(uploadParams, &uploadParam{
					transcodingFilePath: transcodingFilePathMap[key],
					resultVideoUrl:      videoInfo.ResultVideoUrl,
				})
			}
		}
	}
	for _, tmpUploadParam := range uploadParams {
		uploadParam := tmpUploadParam
		wg.Go(func() error {
			return retry.Do(func() error {
				_, err = w.uploadToOBS(ctx, uploadParam.transcodingFilePath, uploadParam.resultVideoUrl)
				if err != nil {
					g.Log().Errorf(ctx, "Failed to upload audio to OBS: %s", err.Error())
					return err
				}
				return nil
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
		})
	}
	err = wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload audio to OBS: %s", err.Error())
		return err
	}
	return nil
}

func genTranscodingKey(videoID int64, resolution string, crf, fps, preset int) string {
	return fmt.Sprintf("%d_%s_%d_%d_%d", videoID, resolution, crf, fps, preset)
}

// 下载音频到临时文件
func (w *VideoTranscodingWorker) downloadVideoToTempFile(ctx context.Context, videoURL string) (string, error) {
	g.Log().Infof(ctx, "Downloading video from URL: %s", videoURL)

	// 创建临时文件
	tempFile, err := os.CreateTemp("", "video_transcoding_*.mp4")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %w", err)
	}
	tempFilePath := tempFile.Name()
	tempFile.Close()

	// 确保OBS客户端已初始化
	obsClient := obs.GetOBSClient()
	if obsClient == nil {
		// 初始化OBS客户端
		obs.InitOBSClient()
		obsClient = obs.GetOBSClient()
		if obsClient == nil {
			return "", fmt.Errorf("failed to initialize OBS client")
		}
	}
	err = obsClient.DownloadFile(videoURL, tempFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to download video file: %w", err)
	}
	return tempFilePath, nil
}

// 上传到OBS
func (w *VideoTranscodingWorker) uploadToOBS(ctx context.Context, localFilePath string, objectKey string) (string, error) {
	g.Log().Infof(ctx, "Uploading audio file to OBS, path: %s, key: %s", localFilePath, objectKey)

	// 确保OBS客户端已初始化
	obsClient := obs.GetOBSClient()
	if obsClient == nil {
		// 初始化OBS客户端
		obs.InitOBSClient()
		obsClient = obs.GetOBSClient()
		if obsClient == nil {
			return "", fmt.Errorf("failed to initialize OBS client")
		}
	}
	// 上传文件到OBS
	url, err := obs.UploadFile(localFilePath, objectKey)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to OBS: %w", err)
	}

	g.Log().Infof(ctx, "Audio file uploaded to OBS successfully, URL: %s", url)
	return url, nil
}
