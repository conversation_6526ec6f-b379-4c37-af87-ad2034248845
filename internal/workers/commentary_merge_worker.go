package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/internal/util"
	"omni_worker/pkg/config"
	"omni_worker/pkg/media"
	hobs "omni_worker/pkg/obs"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type CommentaryMergeSubmitReq struct {
	TaskId           int64   `json:"task_id"`
	SubTaskId        int64   `json:"sub_task_id"`
	VocalAudioPath   string  `json:"vocal_audio_path"` // 在本地盘
	AssFilePath      string  `json:"ass_file_path"`    // 在oss
	IsWaterMark      bool    `json:"is_water_mark"`
	IsVocalTask      bool    `json:"is_vocal_task"`
	NotitleVideoPath string  `json:"notitle_video_path"` // 在oss
	TargetObjectName string  `json:"target_object_name"`
	PostVideoPath    string  `json:"post_video_path"` // 合成配音后的视频路径
	VideoDuration    float32 `json:"video_duration"`  // 视频时长，单位：秒
	Crf              string  `json:"crf"`
	Preset           string  `json:"preset"`
}

type CommentaryMergeEngineRes struct {
	TaskId             int64  `json:"task_id"`
	SubTaskId          int64  `json:"sub_task_id"`
	MergeStartAt       int64  `json:"merge_start_at"`
	MergeEndAt         int64  `json:"merge_end_at"`
	MergeUploadStartAt int64  `json:"merge_upload_start_at"`
	MergeUploadEndAt   int64  `json:"merge_upload_end_at"`
	RetCode            int    `json:"ret_code"`
	ErrMsg             string `json:"err_msg"`
}

type CommentaryMergeSubmitResp struct {
	TaskId  int64  `json:"task_id"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type CommentaryMergeWorker struct {
	*BaseWorker
	taskType         omniEngine.TaskType
	ffmpegThreadCore int
}

func NewCommentaryMergeWorker(taskType omniEngine.TaskType) *CommentaryMergeWorker {
	//if err := media.MediaInit(); err != nil {
	//	g.Log().Errorf(context.Background(), "subtitle merge worker init failed, err: %v", err)
	//	return nil
	//}
	cfg := config.GetConfig()
	return &CommentaryMergeWorker{
		BaseWorker:       NewBaseWorker(),
		taskType:         taskType,
		ffmpegThreadCore: cfg.SubtitleMergeServiceConfig.MergeThreadCore,
	}
}

func (w *CommentaryMergeWorker) Process(ctx context.Context, task *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &CommentaryMergeSubmitReq{}
	err := json.Unmarshal([]byte(task.CommonInputContent), req)
	if err != nil {
		g.Log().Error(ctx, "subtitle merge submit req unmarshal failed, engineId: %v err: %v",
			task.Id, err)
		return omniEngine.TaskStatus_FAILED, nil
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker Process start, taskId: %v, req: %+v", task.Id, *req)
	return w.doSubmitSubtitleMerge(ctx, task, req)
}

func (w *CommentaryMergeWorker) ensureNotitleVideo(ctx context.Context, req *CommentaryMergeSubmitReq) (string, error) {
	if !strings.Contains(req.NotitleVideoPath, "https://") {
		return req.NotitleVideoPath, nil
	}
	// 是https链接
	localPath := filepath.Dir(req.VocalAudioPath)
	err := util.EnsureOutputDirectory(localPath)
	if err != nil {
		return "", err
	}
	notitleVideoLocal := fmt.Sprintf("%s/worker_notitle_%v_%s%s", localPath, req.SubTaskId, uuid.New().String(), filepath.Ext(req.NotitleVideoPath))
	objectName, err := util.GetObjectNameByHttpsUrl(req.NotitleVideoPath)
	if err != nil {
		g.Log().Errorf(ctx, "get notitle video object name failed, err: %v", err)
		return "", err
	}
	err = hobs.GetOBSClient().DownloadFile(objectName, notitleVideoLocal)
	if err != nil {
		g.Log().Errorf(ctx, "download notitle video failed, err: %v", err)
		return "", err
	}
	err = util.EnsureFile(notitleVideoLocal)
	if err != nil {
		g.Log().Errorf(ctx, "check notitle video failed, err: %v", err)
		return "", err
	}
	return notitleVideoLocal, nil
}

func (w *CommentaryMergeWorker) ensureLocal(ctx context.Context, req *CommentaryMergeSubmitReq, remoteUrl string) (string, error) {
	if !strings.Contains(remoteUrl, "https://") {
		return remoteUrl, nil
	}
	// 是https链接
	localPath := filepath.Dir(req.VocalAudioPath)
	err := util.EnsureOutputDirectory(localPath)
	if err != nil {
		return "", err
	}
	localName := fmt.Sprintf("%s/worker_local_%v_%s%s", localPath, req.SubTaskId, uuid.New().String(), filepath.Ext(remoteUrl))
	objectName, err := util.GetObjectNameByHttpsUrl(remoteUrl)
	if err != nil {
		g.Log().Errorf(ctx, "get notitle video object name failed, err: %v", err)
		return "", err
	}
	err = hobs.GetOBSClient().DownloadFile(objectName, localName)
	if err != nil {
		g.Log().Errorf(ctx, "download notitle video failed, err: %v", err)
		return "", err
	}
	err = util.EnsureFile(localName)
	if err != nil {
		g.Log().Errorf(ctx, "check notitle video failed, err: %v", err)
		return "", err
	}
	return localName, nil
}

func (w *CommentaryMergeWorker) doSubmitSubtitleMerge(ctx context.Context, task *omniEngine.Task, req *CommentaryMergeSubmitReq) (
	omniEngine.TaskStatus, *TaskOutputContent,
) {
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, taskId: %v, SubTaskId: %v, engineId: %v",
		req.TaskId, req.SubTaskId, task.Id)
	// 合成视频
	// 上传视频
	// 返回结果
	res := &CommentaryMergeEngineRes{}
	res.RetCode = consts.CommonCodeFail
	res.TaskId = req.TaskId
	res.SubTaskId = req.SubTaskId
	res.MergeStartAt = time.Now().UnixMilli()
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, start merge, taskId: %v, SubTaskId: %v, engineId: %v",
		req.TaskId, req.SubTaskId, task.Id)
	notitleVideoLocal, err := w.ensureNotitleVideo(ctx, req)
	if err != nil {
		g.Log().Errorf(ctx, "check notitle video failed, err: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "ensureNotitleVideo failed." + res.ErrMsg,
			Data:    res,
		}
	}

	localAssFile, err := w.ensureLocal(ctx, req, req.AssFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "check notitle video failed, err: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "ensureLocal failed." + res.ErrMsg,
			Data:    res,
		}
	}
	// 设置超时时间
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Hour)
	defer cancel()
	wg := concurrent.New(timeoutCtx, 1)
	wg.Go(func() error {
		return media.CombineVideoAndSubtitlesWithPreset(req.TaskId, notitleVideoLocal, req.VocalAudioPath, localAssFile, req.PostVideoPath,
			req.Crf, req.Preset, w.ffmpegThreadCore, req.IsWaterMark, req.IsVocalTask)
	})
	err = wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge ffmpeg failed, err: %v", err)
		res.ErrMsg = err.Error()
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "subtitle merge ffmpeg failed." + res.ErrMsg,
			Data:    res,
		}
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, merge over, taskId: %v, SubTaskId: %v, engineId: %v",
		req.TaskId, req.SubTaskId, task.Id)
	res.MergeEndAt = time.Now().UnixMilli()
	// 文件是否存在？
	err = util.EnsureFile(req.PostVideoPath)
	if err != nil {
		g.Log().Errorf(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge file not found, err: %v", err)
		res.ErrMsg = err.Error()
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "subtitle merge file not found." + res.ErrMsg,
			Data:    res,
		}
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, start upload, taskId: %v, SubTaskId: %v, engineId: %v",
		req.TaskId, req.SubTaskId, task.Id)
	// 上传文件
	res.MergeUploadStartAt = time.Now().UnixMilli()
	err = util.UploadAndEnsureMediaFile(req.PostVideoPath, req.TargetObjectName)
	if err != nil {
		g.Log().Errorf(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge upload failed, taskId: %v, SubTaskId: %v, engineId: %v, err: %v",
			req.TaskId, req.SubTaskId, task.Id, err)
		res.ErrMsg = err.Error()
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "subtitle merge upload failed." + res.ErrMsg,
			Data:    res,
		}
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, end upload, taskId: %v, SubTaskId: %v, engineId: %v",
		req.TaskId, req.SubTaskId, task.Id)
	res.MergeUploadEndAt = time.Now().UnixMilli()
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    consts.CommonCodeOk,
		Message: "success",
		Data:    res,
	}
}

func (w *CommentaryMergeWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *CommentaryMergeWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}
