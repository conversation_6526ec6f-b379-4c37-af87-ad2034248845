package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/pkg/config"

	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
)

type VideoHighlightClippingReq struct {
	TaskID              int64   `json:"task_id"`
	VideoURL            string  `json:"video_url"`
	UnderstandingResult string  `json:"understanding_result"`
	GenTime             float64 `json:"gen_time"`
}

type VideoHighlightClippingRes struct {
	Code    int                         `json:"code"`
	Message string                      `json:"message"`
	Data    *VideoHighlightClippingData `json:"data"`
}

type VideoHighlightClippingData struct {
	TaskID        int64           `json:"task_id"`
	ClipDurations []*ClipDuration `json:"clip_durations"`
}

type ClipDuration struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

type VideoHighlightClippingWorker struct {
	*BaseWorker
	VideoHighlightClippingCreateUrl string
	VideoHighlightClippingQueryUrl  string
	taskType                        omniEngine.TaskType
}

func NewVideoHighlightClippingWorker(taskType omniEngine.TaskType) *VideoHighlightClippingWorker {
	cfg := config.GetConfig()
	return &VideoHighlightClippingWorker{
		BaseWorker:                      NewBaseWorker(),
		VideoHighlightClippingCreateUrl: cfg.VideoTranslateService.EraseV2CreateUrl,
		VideoHighlightClippingQueryUrl:  cfg.VideoTranslateService.EraseV2QueryUrl,
		taskType:                        taskType,
	}
}

func (w *VideoHighlightClippingWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *VideoHighlightClippingWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *VideoHighlightClippingWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &VideoHighlightClippingReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "VideoHighlightClippingWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleVideoHighlightClippingTask(ctx, t, req)
}

func (w *VideoHighlightClippingWorker) handleVideoHighlightClippingTask(ctx context.Context, t *omniEngine.Task, req *VideoHighlightClippingReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleVideoHighlightClippingTask] start task, engineId: %v", req.TaskID)
	cfg := config.GetConfig()
	timeoutInterval := time.Duration(cfg.VideoHighlightClippingConfig.TimeoutSeconds) * time.Second
	submitTaskInterval := time.Duration(cfg.VideoHighlightClippingConfig.SubmitTaskIntervalMs) * time.Millisecond
	timeout := time.NewTimer(timeoutInterval)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			errorMsg := fmt.Sprintf("task submit timeout:%fs, taskId:%d", timeoutInterval.Seconds(), req.TaskID)
			g.Log().Errorf(ctx, errorMsg)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: errorMsg,
			}
		default:
			resp := &VideoHighlightClippingRes{}
			err := retry.Do(func() error {
				return w.PostJSON(ctx, cfg.VideoHighlightClippingConfig.CreateUrl, req, resp)
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				errorMsg := fmt.Sprintf("create task err: %s", err)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
			switch resp.Code {
			case CodeSuccess:
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    CodeSuccess,
					Message: resp.Message,
					Data:    resp,
				}
			case CodeAlgorithmBusy:
				time.Sleep(submitTaskInterval)
			default:
				errorMsg := fmt.Sprintf("create task err, code: %d, message: %s", resp.Code, resp.Message)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
		}
	}
}
