package workers

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/stretchr/testify/assert"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
)

func TestBaseWorker_postJsonWithHeader(t *testing.T) {
	type fields struct {
		restClient *trace.XHttpClient
	}
	type args struct {
		ctx          context.Context
		url          string
		requestBody  interface{}
		responseBody interface{}
		headers      map[string]string
	}
	reqHeader := map[string]string{
		XApiAppKey:     "5347218880",
		XApiAccessKey:  "bYA4NSRWjOn5mhjpA-jX183YA1j_VQu5",
		XApiResourceId: "volc.bigasr.auc",
		XApiRequestId:  "11111111222",
		XVolcUser:      "11234566",
	}
	request := VolcengineAsrRequest{
		TaskID: 11111111222,
		User: <PERSON>cengineAsrUser{
			UID: "11234566",
		},
		Audio: VolcengineAsrAudio{
			URL:     "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-test/public/277073271457841181/2025-06-24/6363/audio_translate_50f4c6d7-e241-46e8-9537-840aa2968160.mp3",
			Channel: 1, // 默认单声道
		},
		Request: VolcengineAsrConfig{
			ModelName:         "bigmodel",
			ShowUtterances:    true,
			EnableSpeakerInfo: true,
			Corpus: VolcengineAsrCorpus{
				CorrectTableName: "",
				Context:          "",
			},
		},
	}
	resp := VolcengineAsrResponse{}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    http.Header
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "test_postJsonWithHeader_success",
			fields: fields{
				restClient: trace.NewXHttpClient(),
			},
			args: args{
				ctx:          context.Background(),
				url:          "https://openspeech.bytedance.com/api/v3/auc/bigmodel/submit",
				requestBody:  request,
				responseBody: &resp, // Assuming the response body is a struct
				headers:      reqHeader,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &BaseWorker{
				restClient: tt.fields.restClient,
			}
			respHeader := make(map[string]string)
			err := w.postJsonWithHeader(tt.args.ctx, tt.args.url, tt.args.requestBody, tt.args.responseBody, tt.args.headers, respHeader)
			if err != nil {
				t.Errorf("BaseWorker.postJsonWithHeader() error = %v", err)
				return
			}
			marshal, err := json.Marshal(respHeader)
			if err != nil {
				return
			}
			g.Log().Infof(tt.args.ctx, "Response Header: %v", string(marshal))
		})
	}
}
