package workers

import (
	"context"
	"errors"
	"fmt"
	"time"

	"omni_worker/pkg/config"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type STSRequest struct {
	UserID       int64   `json:"user_id"`
	TaskID       int64   `json:"task_id"`
	SourceUrl    string  `json:"source_url"`
	ReferenceUrl string  `json:"reference_url"`
	TargetUrl    string  `json:"target_url"`
	Similarity   float32 `json:"similarity"`
}

type STSResponse struct {
	Code      int     `json:"code"`
	Message   string  `json:"message"`
	TargetUrl string  `json:"target_url"`
	Duration  float64 `json:"duration"`
}

type STSData struct {
	TargetUrl string  `json:"target_url"`
	Duration  float64 `json:"duration"`
}

type STSWorker struct {
	*BaseWorker
	createSTSUrl        string
	checkSTSUrl         string
	checkResultInterval int // 检查结果的时间间隔（毫秒）
	taskType            omniEngine.TaskType
}

func NewSTSWorker(taskType omniEngine.TaskType) *STSWorker {
	cfg := config.GetConfig()
	checkResultInterval := cfg.STSService.CheckResultIntervalMs
	if checkResultInterval == 0 {
		checkResultInterval = 300
	}
	return &STSWorker{
		BaseWorker:          NewBaseWorker(),
		createSTSUrl:        cfg.STSService.CreateUrl,
		checkSTSUrl:         cfg.STSService.ResultUrl,
		checkResultInterval: checkResultInterval,
		taskType:            taskType,
	}
}

func (s *STSWorker) GetWorkerName() string {
	return config.GetTaskNameByType(s.taskType)
}

func (s *STSWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (s *STSWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting STS task: %d", t.Id)

	request := STSRequest{
		UserID:       t.TenantId,
		TaskID:       t.Id,
		SourceUrl:    t.StsTaskInputContent.SourceUrl,
		ReferenceUrl: t.StsTaskInputContent.ReferenceUrl,
		TargetUrl:    t.StsTaskInputContent.TargetUrl,
		Similarity:   t.StsTaskInputContent.Similarity,
	}
	return s.handleSTSTask(ctx, request)
}

func (s *STSWorker) handleSTSTask(ctx context.Context, request STSRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call STS request: %+v", request)
	if err := s.validateTask(request); err != nil {
		g.Log().Errorf(ctx, "Invalid STS task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid STS task: %v", err),
		}
	}

	var response STSResponse
	err := retry.Do(func() error {
		return s.PostJSON(ctx, s.createSTSUrl, request, &response)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "STS task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("STS task failed: %v", err),
		}
	}
	g.Log().Infof(ctx, "Call STS response: %+v", response)
	return s.handleTaskResponse(ctx, request, &response)
}

func (s *STSWorker) validateTask(request STSRequest) error {
	if !isValidUrl(request.SourceUrl) {
		return errors.New("invalid source_url: must be a valid HTTP/HTTPS Url")
	}
	if !isValidUrl(request.ReferenceUrl) {
		return errors.New("invalid reference_url: must be a valid HTTP/HTTPS Url")
	}
	return nil
}

func (s *STSWorker) handleTaskResponse(ctx context.Context, request STSRequest, response *STSResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 1000:成功，1001:正在处理任务，1003:任务完成
	switch response.Code {
	case CodeCompleted:
		g.Log().Infof(ctx, "STS Task %d completed successfully", request.TaskID)
		return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
			Code:    CodeCompleted,
			Message: StatusCompleted,
			Data:    response.TargetUrl,
		}

	case CodeSuccess, CodeProcessing:
		g.Log().Infof(ctx, "STS Task %d is still processing, checking status later", request.TaskID)
		return s.checkTaskStatus(ctx, request)

	default:
		errorMsg := fmt.Sprintf("STS Task %d failed with code: %v  message: %s", request.TaskID, response.Code, response.Message)
		g.Log().Errorf(ctx, errorMsg)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: errorMsg,
		}
	}
}

func (s *STSWorker) checkTaskStatus(ctx context.Context, stsRequest STSRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	taskId := stsRequest.TaskID
	statusRequest := map[string]interface{}{
		"user_id": stsRequest.UserID,
		"task_id": taskId,
	}
	timeout := time.NewTimer(time.Duration(15) * time.Minute)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "Timeout waiting for STS task %d to complete", taskId)
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: fmt.Sprintf("Timeout waiting for STS task %d to complete", taskId),
			}
		default:
			resp, err := s.restClient.R(ctx).
				SetContext(ctx).
				SetHeader("Content-Type", "application/json").
				SetBody(statusRequest).
				SetResult(&STSResponse{}).
				Post(s.checkSTSUrl)
			if err != nil {
				g.Log().Errorf(ctx, "Failed to call STS status check service: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Failed to call STS status check service: %v", err),
				}
			}

			if resp.StatusCode() != 200 {
				g.Log().Errorf(ctx, "Status check service returned non-200 status: %v", resp.Status())
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Status check service returned non-200 status: %v", resp.Status()),
				}
			}

			statusResponse := resp.Result().(*STSResponse)
			g.Log().Infof(ctx, "Status check response: %+v", statusResponse)

			switch statusResponse.Code {
			case CodeCompleted:
				g.Log().Infof(ctx, "STS task %d completed", taskId)
				data := STSData{
					TargetUrl: statusResponse.TargetUrl,
					Duration:  statusResponse.Duration,
				}
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    CodeCompleted,
					Message: StatusCompleted,
					Data:    data,
				}
			case CodeProcessing:
				g.Log().Infof(ctx, "STS task %d is still processing", taskId)
				time.Sleep(time.Duration(s.checkResultInterval) * time.Millisecond)
			default:
				errorMsg := fmt.Sprintf("STS task %d failed with code: %v  message: %s", taskId, statusResponse.Code, statusResponse.Message)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
		}
	}
}
