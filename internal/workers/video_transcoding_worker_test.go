package workers

import (
	"context"
	"testing"

	"omni_worker/pkg/config"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

func TestVideoTranscodingWorker(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker", t, func() {
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "LTAI5tKgvBShWP92WXvDoHpt",
				SecretKey: "******************************",
				Bucket:    "oss-test-ali-bj-xp-allvoice-cn",
				Endpoint:  "oss-cn-beijing.aliyuncs.com",
				CDN:       "cdn-allvoice-down-cn-testing.funnycp.com",
				CosUrl:    "",
				ObjectDir: "openapi",
			},
		}).Build()
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}
		status, _ := worker.handleVideoTranscodingTask(context.Background(), &VideoTranscodingRequest{
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             3000024790988734013,
							OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250909/82710787-300d-471d-b647-2b2203df8b6f.mp4",
							ResultVideoUrl: "",
						},
						{
							ID:             5307403262150593046,
							OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250909/4dcee50a-d9e3-4948-8da2-23c9868702be.mp4",
							ResultVideoUrl: "",
						},
						// {
						// 	ID:             3051520014136226928,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/36215842-6337-42a3-becb-91a4e441a84a.mp4",
						// 	ResultVideoUrl: "",
						// },
						// {
						// 	ID:             7142377346096516575,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/aef2dd1c-62ca-41f6-ad82-19f95325d03c.mp4",
						// 	ResultVideoUrl: "",
						// },
						// {
						// 	ID:             7762536185581814250,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/2ef1f17f-e3ff-4a47-8cbd-72f9ec7ed57d.mp4",
						// 	ResultVideoUrl: "",
						// },
						// {
						// 	ID:             670790465851376614,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/5d9d2fb7-28cd-442c-9ea0-14e3748e3fac.mp4",
						// 	ResultVideoUrl: "",
						// },
						// 		{
						// 			ID:             5755230026046029809,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/374867c1-6273-486f-aac6-41272cdceff7.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             5774394377206349272,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/339d2fec-095c-4874-89d9-ea0c05c2122a.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2381407969082126810,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/41954a11-3c46-4aa5-8e99-873d034aaf02.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             6722206101051033342,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/d789e29a-1296-4170-b270-a1562a6f4ef8.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             7870225679348222187,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/4a28550b-2ffe-4700-b137-9ae2a62521c3.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             3175618227430601354,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/06d1c96d-9f6a-4ab5-ae1e-e3e5b4ca2988.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2147961666632631946,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/5e25feef-a176-4627-8ad9-f32a22705f88.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             6311436686274938760,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/fc3adbb3-d306-4647-9057-4d0f2af2428b.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             6978628796223238383,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/daa48baf-dc94-4f41-92c3-6ba198b4ca66.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             8764244702096896116,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/4cbcc385-e0d1-4861-a2da-fa8294d68cb8.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             4514469464577493216,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/9ff1f469-a733-4525-acf5-6a7676c843de.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2842623654962413989,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/ec520504-cfa6-4854-b55e-fad46c0831b2.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             7263374278934641766,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/be610d17-4d66-4828-970d-57713ac711db.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2796986615367812807,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/5690bdec-fd01-4079-a8cb-3cc0032222cf.mp4",
						// 			ResultVideoUrl: "",
						// 		},
					},
					CRF:           23,
					FPS:           30,
					Resolution:    "1280x720",
					Mode:          1,
					MergeVideoUrl: "translate-saas-cn/test/public/video_commentary/transcoding/20250905/4762-923f-6ee2304aeb3a.mp4",
					Preset:        0,
				},
			},
		})
		So(status, ShouldEqual, omniEngine.TaskStatus_COMPLETED)
	})
}

func TestValidateVideoParams(t *testing.T) {
	PatchConvey("TestValidateVideoParams", t, func() {
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		// 测试用例1: 有效的参数
		Convey("Should return nil when params are valid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)
		})

		// 测试用例2: 无效的分辨率格式
		Convey("Should return error when resolution format is invalid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280*720", // 使用了*而不是x
					CRF:        28,
					Mode:       onlyTranscodingMode,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "invalid resolution format")
		})

		// 测试用例3: CRF值超出范围
		Convey("Should return error when CRF value is out of range", func() {
			// CRF小于0
			params1 := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        -1,
					Mode:       onlyTranscodingMode,
				},
			}
			err1 := worker.validateVideoParams(params1)
			So(err1, ShouldNotBeNil)
			So(err1.Error(), ShouldContainSubstring, "invalid crf value")

			// CRF大于51
			params2 := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        52,
					Mode:       onlyTranscodingMode,
				},
			}
			err2 := worker.validateVideoParams(params2)
			So(err2, ShouldNotBeNil)
			So(err2.Error(), ShouldContainSubstring, "invalid crf value")
		})

		// 测试用例4: 无效的模式值
		Convey("Should return error when mode is invalid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       999, // 无效的模式
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "invalid mode")
		})

		// 测试用例5: 当模式为transcodingAndMergeMode时缺少MergeVideoUrl
		Convey("Should return error when MergeVideoUrl is missing in transcodingAndMergeMode", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       transcodingAndMergeMode,
					// 缺少MergeVideoUrl
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "merge video url is required")
		})

		// 测试用例6: mode不一致的情况
		Convey("Should return error when modes are inconsistent", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode, // 第一个是只转码模式
				},
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             2,
							OriginVideoUrl: "url2",
							ResultVideoUrl: "result2",
						},
					},
					Resolution:    "1920x1080",
					CRF:           25,
					Mode:          transcodingAndMergeMode, // 第二个是合成模式
					MergeVideoUrl: "merge-url",
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "all video params must have the same mode")
		})

		// 测试用例7: 空参数数组
		Convey("Should return error when params array is empty", func() {
			params := []*VideoParam{}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "video params is empty")
		})

		// 测试用例8: 无效的preset值
		Convey("Should return error when preset is invalid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode,
					Preset:     999, // 无效的preset
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "invalid preset")
		})

		// 测试用例9: 多个有效的只转码模式参数（mode一致）
		Convey("Should return nil when there are multiple valid transcoding-only params with same mode", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
						{
							ID:             2,
							OriginVideoUrl: "url2",
							ResultVideoUrl: "result2",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode,
					Preset:     0,
				},
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             3,
							OriginVideoUrl: "url3",
							ResultVideoUrl: "result3",
						},
					},
					Resolution: "1920x1080",
					CRF:        25,
					Mode:       onlyTranscodingMode, // 相同的模式
					Preset:     1,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)
		})

		// 测试用例10: 多个有效的合成模式参数（mode一致）
		Convey("Should return nil when there are multiple valid merge params with same mode", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
						{
							ID:             2,
							OriginVideoUrl: "url2",
							ResultVideoUrl: "result2",
						},
					},
					Resolution:    "1280x720",
					CRF:           28,
					Mode:          transcodingAndMergeMode,
					MergeVideoUrl: "merge-url-1",
					Preset:        0,
				},
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             3,
							OriginVideoUrl: "url3",
							ResultVideoUrl: "result3",
						},
					},
					Resolution:    "1920x1080",
					CRF:           25,
					Mode:          transcodingAndMergeMode, // 相同的模式
					MergeVideoUrl: "merge-url-2",
					Preset:        1,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)
		})
	})
}
