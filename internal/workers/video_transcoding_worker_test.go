package workers

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"path/filepath"
	"testing"

	"omni_worker/pkg/config"
	"omni_worker/pkg/media"
	"omni_worker/pkg/obs"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

func TestVideoTranscodingWorker(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker", t, func() {
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "LTAI5tKgvBShWP92WXvDoHpt",
				SecretKey: "******************************",
				Bucket:    "oss-test-ali-bj-xp-allvoice-cn",
				Endpoint:  "oss-cn-beijing.aliyuncs.com",
				CDN:       "cdn-allvoice-down-cn-testing.funnycp.com",
				CosUrl:    "",
				ObjectDir: "openapi",
			},
		}).Build()
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}
		status, _ := worker.handleVideoTranscodingTask(context.Background(), &VideoTranscodingRequest{
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             3000024790988734013,
							OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250909/82710787-300d-471d-b647-2b2203df8b6f.mp4",
							ResultVideoUrl: "",
						},
						{
							ID:             5307403262150593046,
							OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250909/4dcee50a-d9e3-4948-8da2-23c9868702be.mp4",
							ResultVideoUrl: "",
						},
						// {
						// 	ID:             3051520014136226928,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/36215842-6337-42a3-becb-91a4e441a84a.mp4",
						// 	ResultVideoUrl: "",
						// },
						// {
						// 	ID:             7142377346096516575,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/aef2dd1c-62ca-41f6-ad82-19f95325d03c.mp4",
						// 	ResultVideoUrl: "",
						// },
						// {
						// 	ID:             7762536185581814250,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/2ef1f17f-e3ff-4a47-8cbd-72f9ec7ed57d.mp4",
						// 	ResultVideoUrl: "",
						// },
						// {
						// 	ID:             670790465851376614,
						// 	OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/5d9d2fb7-28cd-442c-9ea0-14e3748e3fac.mp4",
						// 	ResultVideoUrl: "",
						// },
						// 		{
						// 			ID:             5755230026046029809,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/374867c1-6273-486f-aac6-41272cdceff7.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             5774394377206349272,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/339d2fec-095c-4874-89d9-ea0c05c2122a.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2381407969082126810,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/41954a11-3c46-4aa5-8e99-873d034aaf02.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             6722206101051033342,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/d789e29a-1296-4170-b270-a1562a6f4ef8.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             7870225679348222187,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/4a28550b-2ffe-4700-b137-9ae2a62521c3.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             3175618227430601354,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/06d1c96d-9f6a-4ab5-ae1e-e3e5b4ca2988.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2147961666632631946,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/5e25feef-a176-4627-8ad9-f32a22705f88.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             6311436686274938760,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/fc3adbb3-d306-4647-9057-4d0f2af2428b.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             6978628796223238383,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/daa48baf-dc94-4f41-92c3-6ba198b4ca66.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             8764244702096896116,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/4cbcc385-e0d1-4861-a2da-fa8294d68cb8.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             4514469464577493216,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/9ff1f469-a733-4525-acf5-6a7676c843de.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2842623654962413989,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/ec520504-cfa6-4854-b55e-fad46c0831b2.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             7263374278934641766,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/be610d17-4d66-4828-970d-57713ac711db.mp4",
						// 			ResultVideoUrl: "",
						// 		},
						// 		{
						// 			ID:             2796986615367812807,
						// 			OriginVideoUrl: "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-cn/test/public/video_commentary/trimclip/20250905/5690bdec-fd01-4079-a8cb-3cc0032222cf.mp4",
						// 			ResultVideoUrl: "",
						// 		},
					},
					CRF:           23,
					FPS:           30,
					Resolution:    "1280x720",
					Mode:          1,
					MergeVideoUrl: "translate-saas-cn/test/public/video_commentary/transcoding/20250905/4762-923f-6ee2304aeb3a.mp4",
					Preset:        0,
				},
			},
		})
		So(status, ShouldEqual, omniEngine.TaskStatus_COMPLETED)
	})
}

func TestValidateVideoParams(t *testing.T) {
	PatchConvey("TestValidateVideoParams", t, func() {
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		// 测试用例1: 有效的参数
		Convey("Should return nil when params are valid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)
		})

		// 测试用例2: 无效的分辨率格式
		Convey("Should return error when resolution format is invalid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280*720", // 使用了*而不是x
					CRF:        28,
					Mode:       onlyTranscodingMode,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "invalid resolution format")
		})

		// 测试用例3: CRF值超出范围
		Convey("Should return error when CRF value is out of range", func() {
			// CRF小于0
			params1 := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        -1,
					Mode:       onlyTranscodingMode,
				},
			}
			err1 := worker.validateVideoParams(params1)
			So(err1, ShouldNotBeNil)
			So(err1.Error(), ShouldContainSubstring, "invalid crf value")

			// CRF大于51
			params2 := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        52,
					Mode:       onlyTranscodingMode,
				},
			}
			err2 := worker.validateVideoParams(params2)
			So(err2, ShouldNotBeNil)
			So(err2.Error(), ShouldContainSubstring, "invalid crf value")
		})

		// 测试用例4: 无效的模式值
		Convey("Should return error when mode is invalid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       999, // 无效的模式
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "invalid mode")
		})

		// 测试用例5: 当模式为transcodingAndMergeMode时缺少MergeVideoUrl
		Convey("Should return error when MergeVideoUrl is missing in transcodingAndMergeMode", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       transcodingAndMergeMode,
					// 缺少MergeVideoUrl
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "merge video url is required")
		})

		// 测试用例6: mode不一致的情况
		Convey("Should return error when modes are inconsistent", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode, // 第一个是只转码模式
				},
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             2,
							OriginVideoUrl: "url2",
							ResultVideoUrl: "result2",
						},
					},
					Resolution:    "1920x1080",
					CRF:           25,
					Mode:          transcodingAndMergeMode, // 第二个是合成模式
					MergeVideoUrl: "merge-url",
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "all video params must have the same mode")
		})

		// 测试用例7: 空参数数组
		Convey("Should return error when params array is empty", func() {
			params := []*VideoParam{}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "video params is empty")
		})

		// 测试用例8: 无效的preset值
		Convey("Should return error when preset is invalid", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode,
					Preset:     999, // 无效的preset
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "invalid preset")
		})

		// 测试用例9: 多个有效的只转码模式参数（mode一致）
		Convey("Should return nil when there are multiple valid transcoding-only params with same mode", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
						{
							ID:             2,
							OriginVideoUrl: "url2",
							ResultVideoUrl: "result2",
						},
					},
					Resolution: "1280x720",
					CRF:        28,
					Mode:       onlyTranscodingMode,
					Preset:     0,
				},
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             3,
							OriginVideoUrl: "url3",
							ResultVideoUrl: "result3",
						},
					},
					Resolution: "1920x1080",
					CRF:        25,
					Mode:       onlyTranscodingMode, // 相同的模式
					Preset:     1,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)
		})

		// 测试用例10: 多个有效的合成模式参数（mode一致）
		Convey("Should return nil when there are multiple valid merge params with same mode", func() {
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "url1",
							ResultVideoUrl: "result1",
						},
						{
							ID:             2,
							OriginVideoUrl: "url2",
							ResultVideoUrl: "result2",
						},
					},
					Resolution:    "1280x720",
					CRF:           28,
					Mode:          transcodingAndMergeMode,
					MergeVideoUrl: "merge-url-1",
					Preset:        0,
				},
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             3,
							OriginVideoUrl: "url3",
							ResultVideoUrl: "result3",
						},
					},
					Resolution:    "1920x1080",
					CRF:           25,
					Mode:          transcodingAndMergeMode, // 相同的模式
					MergeVideoUrl: "merge-url-2",
					Preset:        1,
				},
			}
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)
		})
	})
}

// createMockVideoFile 创建一个模拟的视频文件用于测试
func createMockVideoFile(filePath string) error {
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入一些模拟数据
	_, err = file.WriteString("mock video content")
	return err
}

// TestVideoTranscodingWorker_TranscodingMode 测试只转码模式
func TestVideoTranscodingWorker_TranscodingMode(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_TranscodingMode", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件
		Mock((*utils.ObjectStorage).DownloadFile).To(func(objectKey, localFilePath string) error {
			// 创建一个模拟的视频文件
			return createMockVideoFile(localFilePath)
		}).Build()

		// Mock上传文件
		Mock(obs.UploadFile).Return("https://test-cdn.com/uploaded-file.mp4", nil).Build()

		// Mock视频验证
		Mock(media.VideoValidate).Return(nil).Build()

		// Mock视频转码
		Mock(media.VideoTranscoding).To(func(ctx context.Context, videoFile, outputPath, resolution, preset string, crf, fps int) error {
			// 创建一个模拟的转码后文件
			return createMockVideoFile(outputPath)
		}).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		// 测试只转码模式
		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1001,
							OriginVideoUrl: "test-video-1.mp4",
							ResultVideoUrl: "result-video-1.mp4",
						},
						{
							ID:             1002,
							OriginVideoUrl: "test-video-2.mp4",
							ResultVideoUrl: "result-video-2.mp4",
						},
					},
					FPS:        30,
					CRF:        28,
					Resolution: "1280x720",
					Preset:     0, // ultrafast
					Mode:       onlyTranscodingMode,
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_COMPLETED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeSuccess)
		So(output.Message, ShouldEqual, "success")
	})
}
// TestVideoTranscodingWorker_MergeMode 测试合并模式
func TestVideoTranscodingWorker_MergeMode(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_MergeMode", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件
		Mock((*utils.ObjectStorage).DownloadFile).To(func(objectKey, localFilePath string) error {
			return createMockVideoFile(localFilePath)
		}).Build()

		// Mock上传文件
		Mock(obs.UploadFile).Return("https://test-cdn.com/merged-file.mp4", nil).Build()

		// Mock视频验证
		Mock(media.VideoValidate).Return(nil).Build()

		// Mock合并并转码视频
		Mock(media.MergeAndTranscodeVideos).To(func(ctx context.Context, filePaths []string, output, resolution, preset string, crf, fps int) error {
			return createMockVideoFile(output)
		}).Build()

		// Mock音量归一化
		Mock(media.NormalizeAudio).To(func(ctx context.Context, inputPath, outputPath string) error {
			return createMockVideoFile(outputPath)
		}).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		// 测试合并模式 - 多个视频
		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             2001,
							OriginVideoUrl: "test-video-1.mp4",
							ResultVideoUrl: "result-video-1.mp4",
						},
						{
							ID:             2002,
							OriginVideoUrl: "test-video-2.mp4",
							ResultVideoUrl: "result-video-2.mp4",
						},
						{
							ID:             2003,
							OriginVideoUrl: "test-video-3.mp4",
							ResultVideoUrl: "result-video-3.mp4",
						},
					},
					FPS:           30,
					CRF:           25,
					Resolution:    "1920x1080",
					Preset:        1, // slow
					Mode:          transcodingAndMergeMode,
					MergeVideoUrl: "merged-output.mp4",
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_COMPLETED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeSuccess)
		So(output.Message, ShouldEqual, "success")
	})
}
// TestVideoTranscodingWorker_SingleVideoMergeMode 测试合并模式单个视频
func TestVideoTranscodingWorker_SingleVideoMergeMode(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_SingleVideoMergeMode", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件
		Mock((*utils.ObjectStorage).DownloadFile).To(func(objectKey, localFilePath string) error {
			return createMockVideoFile(localFilePath)
		}).Build()

		// Mock上传文件
		Mock(obs.UploadFile).Return("https://test-cdn.com/single-video.mp4", nil).Build()

		// Mock视频验证
		Mock(media.VideoValidate).Return(nil).Build()

		// Mock视频转码（单个视频不需要合并，直接转码）
		Mock(media.VideoTranscoding).To(func(ctx context.Context, videoFile, outputPath, resolution, preset string, crf, fps int) error {
			return createMockVideoFile(outputPath)
		}).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		// 测试合并模式 - 单个视频（应该直接转码而不是合并）
		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             3001,
							OriginVideoUrl: "test-single-video.mp4",
							ResultVideoUrl: "result-single-video.mp4",
						},
					},
					FPS:           30,
					CRF:           25,
					Resolution:    "1920x1080",
					Preset:        2, // medium
					Mode:          transcodingAndMergeMode,
					MergeVideoUrl: "single-output.mp4",
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_COMPLETED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeSuccess)
		So(output.Message, ShouldEqual, "success")
	})
}
// TestVideoTranscodingWorker_DownloadFailure 测试下载失败场景
func TestVideoTranscodingWorker_DownloadFailure(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_DownloadFailure", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件失败
		Mock((*utils.ObjectStorage).DownloadFile).Return(errors.New("download failed")).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             4001,
							OriginVideoUrl: "test-video-fail.mp4",
							ResultVideoUrl: "result-video-fail.mp4",
						},
					},
					FPS:        30,
					CRF:        28,
					Resolution: "1280x720",
					Preset:     0,
					Mode:       onlyTranscodingMode,
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeTaskFailed)
		So(output.Message, ShouldEqual, "download video failed")
	})
}
// TestVideoTranscodingWorker_VideoValidationFailure 测试视频验证失败场景
func TestVideoTranscodingWorker_VideoValidationFailure(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_VideoValidationFailure", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件成功
		Mock((*utils.ObjectStorage).DownloadFile).To(func(objectKey, localFilePath string) error {
			return createMockVideoFile(localFilePath)
		}).Build()

		// Mock视频验证失败
		Mock(media.VideoValidate).Return(errors.New("invalid video format")).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             5001,
							OriginVideoUrl: "test-invalid-video.mp4",
							ResultVideoUrl: "result-invalid-video.mp4",
						},
					},
					FPS:        30,
					CRF:        28,
					Resolution: "1280x720",
					Preset:     0,
					Mode:       onlyTranscodingMode,
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeTaskFailed)
		So(output.Message, ShouldEqual, "invalid video file")
	})
}
// TestVideoTranscodingWorker_TranscodingFailure 测试转码失败场景
func TestVideoTranscodingWorker_TranscodingFailure(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_TranscodingFailure", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件成功
		Mock((*utils.ObjectStorage).DownloadFile).To(func(objectKey, localFilePath string) error {
			return createMockVideoFile(localFilePath)
		}).Build()

		// Mock视频验证成功
		Mock(media.VideoValidate).Return(nil).Build()

		// Mock视频转码失败
		Mock(media.VideoTranscoding).Return(errors.New("transcoding failed")).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             6001,
							OriginVideoUrl: "test-video.mp4",
							ResultVideoUrl: "result-video.mp4",
						},
					},
					FPS:        30,
					CRF:        28,
					Resolution: "1280x720",
					Preset:     0,
					Mode:       onlyTranscodingMode,
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeTaskFailed)
		So(output.Message, ShouldEqual, "video transcoding failed")
	})
}
// TestVideoTranscodingWorker_MergeFailure 测试合并失败场景
func TestVideoTranscodingWorker_MergeFailure(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_MergeFailure", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件成功
		Mock((*utils.ObjectStorage).DownloadFile).To(func(objectKey, localFilePath string) error {
			return createMockVideoFile(localFilePath)
		}).Build()

		// Mock视频验证成功
		Mock(media.VideoValidate).Return(nil).Build()

		// Mock合并并转码失败
		Mock(media.MergeAndTranscodeVideos).Return(errors.New("merge failed")).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             7001,
							OriginVideoUrl: "test-video-1.mp4",
							ResultVideoUrl: "result-video-1.mp4",
						},
						{
							ID:             7002,
							OriginVideoUrl: "test-video-2.mp4",
							ResultVideoUrl: "result-video-2.mp4",
						},
					},
					FPS:           30,
					CRF:           25,
					Resolution:    "1920x1080",
					Preset:        1,
					Mode:          transcodingAndMergeMode,
					MergeVideoUrl: "merged-output.mp4",
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeTaskFailed)
		So(output.Message, ShouldEqual, "video merge and upload failed")
	})
}
// TestVideoTranscodingWorker_UploadFailure 测试上传失败场景
func TestVideoTranscodingWorker_UploadFailure(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_UploadFailure", t, func() {
		// Mock配置
		Mock(config.GetConfig).Return(&config.Config{
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "test-access-key",
				SecretKey: "test-secret-key",
				Bucket:    "test-bucket",
				Endpoint:  "test-endpoint",
				CDN:       "test-cdn",
				ObjectDir: "test",
			},
		}).Build()

		// Mock OBS客户端
		mockOBSClient := &utils.ObjectStorage{}
		Mock(obs.GetOBSClient).Return(mockOBSClient).Build()
		Mock(obs.InitOBSClient).Return().Build()

		// Mock下载文件成功
		Mock((*utils.ObjectStorage).DownloadFile).To(func(objectKey, localFilePath string) error {
			return createMockVideoFile(localFilePath)
		}).Build()

		// Mock视频验证成功
		Mock(media.VideoValidate).Return(nil).Build()

		// Mock视频转码成功
		Mock(media.VideoTranscoding).To(func(ctx context.Context, videoFile, outputPath, resolution, preset string, crf, fps int) error {
			return createMockVideoFile(outputPath)
		}).Build()

		// Mock上传失败
		Mock(obs.UploadFile).Return("", errors.New("upload failed")).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		request := &VideoTranscodingRequest{
			TaskID: 12345,
			VideoParams: []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             8001,
							OriginVideoUrl: "test-video.mp4",
							ResultVideoUrl: "result-video.mp4",
						},
					},
					FPS:        30,
					CRF:        28,
					Resolution: "1280x720",
					Preset:     0,
					Mode:       onlyTranscodingMode,
				},
			},
		}

		status, output := worker.handleVideoTranscodingTask(context.Background(), request)

		So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
		So(output, ShouldNotBeNil)
		So(output.Code, ShouldEqual, CodeTaskFailed)
		So(output.Message, ShouldEqual, "video upload failed")
	})
}

// TestVideoTranscodingWorker_Process 测试Process方法
func TestVideoTranscodingWorker_Process(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_Process", t, func() {
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		// 测试JSON解析失败
		Convey("Should return FAILED when JSON unmarshal fails", func() {
			task := &omniEngine.Task{
				Id:                  12345,
				CommonInputContent: "invalid json",
			}

			status, output := worker.Process(context.Background(), task)

			So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
			So(output, ShouldBeNil)
		})

		// 测试JSON解析成功但参数验证失败
		Convey("Should return FAILED when params validation fails", func() {
			request := &VideoTranscodingRequest{
				TaskID: 12345,
				VideoParams: []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "invalid-resolution", // 无效的分辨率格式
						CRF:        28,
						Mode:       onlyTranscodingMode,
					},
				},
			}

			jsonBytes, _ := json.Marshal(request)
			task := &omniEngine.Task{
				Id:                  12345,
				CommonInputContent: string(jsonBytes),
			}

			status, output := worker.Process(context.Background(), task)

			So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
			So(output, ShouldNotBeNil)
			So(output.Code, ShouldEqual, CodeTaskFailed)
			So(output.Message, ShouldContainSubstring, "validate video params failed")
		})
	})
}

// TestVideoTranscodingWorker_GetWorkerName 测试GetWorkerName方法
func TestVideoTranscodingWorker_GetWorkerName(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_GetWorkerName", t, func() {
		Mock(config.GetTaskNameByType).To(func(taskType omniEngine.TaskType) string {
			return "video_transcoding_worker"
		}).Build()

		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		name := worker.GetWorkerName()
		So(name, ShouldEqual, "video_transcoding_worker")
	})
}

// TestVideoTranscodingWorker_Callback 测试Callback方法
func TestVideoTranscodingWorker_Callback(t *testing.T) {
	PatchConvey("TestVideoTranscodingWorker_Callback", t, func() {
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		task := &omniEngine.Task{
			Id: 12345,
		}

		status, output := worker.Callback(context.Background(), task, nil)

		So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
		So(output, ShouldBeNil)
	})
}