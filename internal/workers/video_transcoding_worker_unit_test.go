package workers

import (
	"context"
	"encoding/json"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

// TestVideoTranscodingWorker_UnitTests 专注于单元测试，不依赖外部服务
func TestVideoTranscodingWorker_UnitTests(t *testing.T) {
	<PERSON>vey("VideoTranscodingWorker Unit Tests", t, func() {
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		<PERSON>vey("Test validateVideoParams", func() {
			Convey("Should return nil for valid params", func() {
				params := []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "1280x720",
						CRF:        28,
						FPS:        30,
						Preset:     0,
						Mode:       onlyTranscodingMode,
					},
				}
				err := worker.validateVideoParams(params)
				So(err, ShouldBeNil)
			})

			Convey("Should return error for invalid resolution", func() {
				params := []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "1280*720", // 错误格式
						CRF:        28,
						Mode:       onlyTranscodingMode,
					},
				}
				err := worker.validateVideoParams(params)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "invalid resolution format")
			})

			Convey("Should return error for invalid CRF", func() {
				params := []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "1280x720",
						CRF:        -1, // 无效值
						Mode:       onlyTranscodingMode,
					},
				}
				err := worker.validateVideoParams(params)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "invalid crf value")
			})

			Convey("Should return error for invalid mode", func() {
				params := []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "1280x720",
						CRF:        28,
						Mode:       999, // 无效模式
					},
				}
				err := worker.validateVideoParams(params)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "invalid mode")
			})

			Convey("Should return error when merge URL missing in merge mode", func() {
				params := []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "1280x720",
						CRF:        28,
						Mode:       transcodingAndMergeMode,
						// MergeVideoUrl 缺失
					},
				}
				err := worker.validateVideoParams(params)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "merge video url is required")
			})

			Convey("Should return error for inconsistent modes", func() {
				params := []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "1280x720",
						CRF:        28,
						Mode:       onlyTranscodingMode,
					},
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             2,
								OriginVideoUrl: "url2",
								ResultVideoUrl: "result2",
							},
						},
						Resolution:    "1920x1080",
						CRF:           25,
						Mode:          transcodingAndMergeMode,
						MergeVideoUrl: "merge-url",
					},
				}
				err := worker.validateVideoParams(params)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "all video params must have the same mode")
			})

			Convey("Should return error for invalid preset", func() {
				params := []*VideoParam{
					{
						VideoInfos: []*VideoInfo{
							{
								ID:             1,
								OriginVideoUrl: "url1",
								ResultVideoUrl: "result1",
							},
						},
						Resolution: "1280x720",
						CRF:        28,
						Mode:       onlyTranscodingMode,
						Preset:     999, // 无效preset
					},
				}
				err := worker.validateVideoParams(params)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "invalid preset")
			})

			Convey("Should return error for empty params", func() {
				params := []*VideoParam{}
				err := worker.validateVideoParams(params)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "video params is empty")
			})
		})

		Convey("Test genTranscodingKey", func() {
			key := genTranscodingKey(123, "1280x720", 28, 30, 0)
			So(key, ShouldEqual, "123_1280x720_28_30_0")
		})

		Convey("Test Process method", func() {
			Convey("Should return FAILED for invalid JSON", func() {
				task := &omniEngine.Task{
					Id:                  12345,
					CommonInputContent: "invalid json",
				}

				status, output := worker.Process(context.Background(), task)

				So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
				So(output, ShouldBeNil)
			})

			Convey("Should return FAILED for invalid params", func() {
				request := &VideoTranscodingRequest{
					TaskID: 12345,
					VideoParams: []*VideoParam{
						{
							VideoInfos: []*VideoInfo{
								{
									ID:             1,
									OriginVideoUrl: "url1",
									ResultVideoUrl: "result1",
								},
							},
							Resolution: "invalid-resolution",
							CRF:        28,
							Mode:       onlyTranscodingMode,
						},
					},
				}

				jsonBytes, _ := json.Marshal(request)
				task := &omniEngine.Task{
					Id:                  12345,
					CommonInputContent: string(jsonBytes),
				}

				status, output := worker.Process(context.Background(), task)

				So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
				So(output, ShouldNotBeNil)
				So(output.Code, ShouldEqual, CodeTaskFailed)
				So(output.Message, ShouldContainSubstring, "validate video params failed")
			})
		})

		Convey("Test Callback method", func() {
			task := &omniEngine.Task{Id: 12345}
			status, output := worker.Callback(context.Background(), task, nil)

			So(status, ShouldEqual, omniEngine.TaskStatus_FAILED)
			So(output, ShouldBeNil)
		})
	})
}

// TestPresetMap 测试预设映射
func TestPresetMap(t *testing.T) {
	Convey("Test preset mapping", t, func() {
		So(presetMap[0], ShouldEqual, "ultrafast")
		So(presetMap[1], ShouldEqual, "slow")
		So(presetMap[2], ShouldEqual, "medium")
		So(presetMap[3], ShouldEqual, "fast")
	})
}

// TestConstants 测试常量定义
func TestConstants(t *testing.T) {
	Convey("Test constants", t, func() {
		So(onlyTranscodingMode, ShouldEqual, 0)
		So(transcodingAndMergeMode, ShouldEqual, 1)
	})
}

// TestVideoTranscodingWorker_TranscodingLogic 测试转码逻辑
func TestVideoTranscodingWorker_TranscodingLogic(t *testing.T) {
	Convey("Test transcoding logic", t, func() {
		worker := &VideoTranscodingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_TRANSCODING,
		}

		Convey("Test transcoding mode logic", func() {
			// 测试只转码模式的逻辑
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "test-video-1.mp4",
							ResultVideoUrl: "result-video-1.mp4",
						},
						{
							ID:             2,
							OriginVideoUrl: "test-video-2.mp4",
							ResultVideoUrl: "result-video-2.mp4",
						},
					},
					FPS:        30,
					CRF:        28,
					Resolution: "1280x720",
					Preset:     0,
					Mode:       onlyTranscodingMode,
				},
			}

			// 验证参数有效性
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)

			// 验证模式判断
			So(params[0].Mode, ShouldEqual, onlyTranscodingMode)
			So(len(params[0].VideoInfos), ShouldEqual, 2)
		})

		Convey("Test merge mode logic", func() {
			// 测试合并模式的逻辑
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "test-video-1.mp4",
							ResultVideoUrl: "result-video-1.mp4",
						},
						{
							ID:             2,
							OriginVideoUrl: "test-video-2.mp4",
							ResultVideoUrl: "result-video-2.mp4",
						},
						{
							ID:             3,
							OriginVideoUrl: "test-video-3.mp4",
							ResultVideoUrl: "result-video-3.mp4",
						},
					},
					FPS:           30,
					CRF:           25,
					Resolution:    "1920x1080",
					Preset:        1,
					Mode:          transcodingAndMergeMode,
					MergeVideoUrl: "merged-output.mp4",
				},
			}

			// 验证参数有效性
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)

			// 验证模式判断
			So(params[0].Mode, ShouldEqual, transcodingAndMergeMode)
			So(len(params[0].VideoInfos), ShouldEqual, 3)
			So(params[0].MergeVideoUrl, ShouldNotBeEmpty)
		})

		Convey("Test single video in merge mode", func() {
			// 测试合并模式下单个视频的情况
			params := []*VideoParam{
				{
					VideoInfos: []*VideoInfo{
						{
							ID:             1,
							OriginVideoUrl: "test-single-video.mp4",
							ResultVideoUrl: "result-single-video.mp4",
						},
					},
					FPS:           30,
					CRF:           25,
					Resolution:    "1920x1080",
					Preset:        2,
					Mode:          transcodingAndMergeMode,
					MergeVideoUrl: "single-output.mp4",
				},
			}

			// 验证参数有效性
			err := worker.validateVideoParams(params)
			So(err, ShouldBeNil)

			// 单个视频在合并模式下应该直接转码而不是合并
			So(len(params[0].VideoInfos), ShouldEqual, 1)
		})
	})
}
