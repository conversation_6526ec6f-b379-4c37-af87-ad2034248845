package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

// 预处理提交请求
type SubtitlePreprocessProSubmitReq struct {
	UserID       int64       `json:"user_id"`
	Id           int64       `json:"id"`
	TaskType     string      `json:"task_type"`
	VideoUrl     string      `json:"video_url"`
	StartMSecond int64       `json:"start_msecond"`
	EndMSecond   int64       `json:"end_msecond"`
	BboxesFile   []BBoxes    `json:"bboxes_file"`
	InpaintMasks []EraseArea `json:"video_inpaint_masks"`
	FullScreen   bool        `json:"full_screen"`
	ModelVersion string      `json:"model_version"`
}

type EraseAreaPro struct {
	ActionType string    `json:"type"`
	Start      float32   `json:"start"`
	End        float32   `json:"end"`
	Region     []float64 `json:"region"`
}

type BBoxesPro struct {
	FrameIdx    int32     `json:"frame_idx"`
	Bboxes      [][]int32 `json:"bboxes"`
	IsSubtitles []bool    `json:"is_subtitles"`
	IsErase     []bool    `json:"is_erase"`
	Texts       []string  `json:"texts"`
}

// 预处理提交返回
type SubtitlePreprocessProSubmitRes struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

type SubtitlePreprocessProQueryReq struct {
	Id int64 `json:"id"`
}

// 预处理结果返回
type SubtitlePreprocessProQueryRes struct {
	Id            int64             `json:"id"`
	Code          int32             `json:"code"`
	Msg           string            `json:"message"`
	Spent         int64             `json:"spent"`
	TotalChunk    int32             `json:"total_chunk"`
	ProgressRatio float32           `json:"progress_ratio"`
	Chunks        []PreprocessChunk `json:"chunks"`
}

type PreprocessProChunk struct {
	ChunkId      int32     `json:"chunk_idx"`
	FramesRange  []int32   `json:"frames_range"`
	ChunkBbox    []int32   `json:"chunk_bbox"`
	InferBbox    [][]int32 `json:"infer_bbox"`
	ChunkPath    string    `json:"chunk_path"`
	MasterFrames []int32   `json:"master_frames"`
	InferSize    []int32   `json:"infer_size"`
	OccupiedSize float32   `json:"occupied_size"`
}

type SubtitlePreprocessProData struct {
	Id            int64             `json:"id"`
	Spent         int64             `json:"spent"`
	TotalChunk    int32             `json:"total_chunk"`
	ProgressRatio float32           `json:"progress_ratio"`
	Chunks        []PreprocessChunk `json:"chunks"`
}

type SubtitlePreprocessProWorker struct {
	*BaseWorker
	preprocessV2CreateUrl string
	preprocessV2QueryUrl  string
	taskType              omniEngine.TaskType
}

func (w *SubtitlePreprocessProWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *SubtitlePreprocessProWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &SubtitlePreprocessProSubmitReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "SubtitlePreprocessProWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.query(ctx, t, req)
}

func NewSubtitlePreprocessProWorker(taskType omniEngine.TaskType) *SubtitlePreprocessProWorker {
	cfg := config.GetConfig()
	return &SubtitlePreprocessProWorker{
		BaseWorker:            NewBaseWorker(),
		preprocessV2CreateUrl: cfg.VideoTranslateService.PreprocessV2CreateUrl,
		preprocessV2QueryUrl:  cfg.VideoTranslateService.PreprocessV2QueryUrl,
		taskType:              taskType,
	}
}

func (w *SubtitlePreprocessProWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &SubtitlePreprocessProSubmitReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "SubtitlePreprocessProWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	req.ModelVersion = consts.EraseModelPro
	return w.handleSubtitlePreprocessProTask(ctx, t, req)
}

func (w *SubtitlePreprocessProWorker) handleSubtitlePreprocessProTask(ctx context.Context, t *omniEngine.Task, req *SubtitlePreprocessProSubmitReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleSubtitlePreprocessProTask] start task, engineId: %v, taskId: %v", t.Id, req.Id)
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[handleSubtitlePreprocessProTask] timeout, engineId:%d, taskId: %v", t.Id, req.Id)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "submit task timeout",
			}
		default:
			status, res := w.submitTask(ctx, t, req)
			if status == omniEngine.TaskStatus_NOT_STARTED {
				time.Sleep(time.Second)
			} else {
				return status, res
			}
		}
	}
}

func (w *SubtitlePreprocessProWorker) submitTask(ctx context.Context, t *omniEngine.Task, req *SubtitlePreprocessProSubmitReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	res := &SubtitlePreprocessSubmitRes{}
	err := retry.Do(func() error {
		return w.PostJSON(ctx, w.preprocessV2CreateUrl, req, res)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "[submitTask] HTTP req failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("http req failed:%s", err),
		}
	}
	switch res.Code {
	case consts.AiCodeOk:
		g.Log().Infof(ctx, "[submitTask] submit ok, engineId: %v, taskId: %v", t.Id, req.Id)
		return omniEngine.TaskStatus_IN_PROGRESS, &TaskOutputContent{
			Code:    consts.CommonCodeOk,
			Message: "ok",
			Data:    res,
		}
	case consts.AiCodeBusy:
		// 预处理繁忙，继续尝试提交
		g.Log().Infof(ctx, "[submitTask] subtitle preprocess busy, retry commit, msg:%s, id: %v, engineId: %v", res.Msg, req.Id, t.Id)
		return omniEngine.TaskStatus_NOT_STARTED, nil
	case consts.AiCodeParamsErr:
		g.Log().Errorf(ctx, "[submitTask] task failed, err:%s, content: %+v", res.Msg, req)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("task failed:%s", res.Msg),
			Data:    res,
		}
	default:
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("unknown code:%d", res.Code),
			Data:    res,
		}
	}
}

func (w *SubtitlePreprocessProWorker) query(ctx context.Context, t *omniEngine.Task, req *SubtitlePreprocessProSubmitReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 构建查询请求
	queryReq := &SubtitlePreprocessQueryReq{Id: req.Id}
	resp := &SubtitlePreprocessQueryRes{}
	beginTime := time.Now()
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	retryCount := 0
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[query] timeout, engineId: %v, taskId: %v, beginTime: %v",
				t.Id, req.Id, beginTime.Format(time.DateTime))
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "query result timeout",
			}
		default:
			err := retry.Do(func() error {
				return w.PostJSON(ctx, w.preprocessV2QueryUrl, queryReq, resp)
			},
				retry.Attempts(5),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "[query] http request failed: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("http request failed:%s", err),
				}
			}
			switch resp.Code {
			case consts.AiCodeOk:
				g.Log().Infof(ctx, "[query] task completed, engineId: %v, taskId: %v, timeCost: %v",
					t.Id, req.Id, time.Since(beginTime).Seconds())
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    consts.CommonCodeOk,
					Message: "ok",
					Data:    resp,
				}
			case consts.AiCodeBusy:
				g.Log().Infof(ctx, "[query] subtitle preprocess pro in progress, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				time.Sleep(time.Second)
			case consts.AiCodeTaskNotExist, consts.AiCodeCudaOutOfMemory:
				g.Log().Infof(ctx, "[query] retry submit pro task, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				retryCount++
				if retryCount > 3 {
					return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
						Code:    consts.CommonCodeFail,
						Message: "attempt all retry failed",
					}
				}
				// 任务不存在或者内存不足，需要重新提交
				status, res := w.handleSubtitlePreprocessProTask(ctx, t, req)
				// 重新提交成功，继续查询结果
				if status == omniEngine.TaskStatus_IN_PROGRESS {
					continue
				}
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: "retry submit pro task failed",
					Data:    res,
				}
			case consts.AiCodePullVideoFailed, consts.AiCodeParamsErr,
				consts.AiCodeInternalError:
				g.Log().Infof(ctx, "[query] task failed, taskId: %v, engineId: %v, code: %v, msg: %s",
					req.Id, t.Id, resp.Code, resp.Msg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: resp.Msg,
					Data:    resp,
				}
			default:
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("unknown code:%d", resp.Code),
					Data:    resp,
				}
			}
		}
	}
}
