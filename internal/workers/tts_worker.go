package workers

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"omni_worker/internal/monitor"
	"omni_worker/pkg/config"

	"github.com/samber/lo"

	"github.com/go-resty/resty/v2"
	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

const (
	TypeTTS       = "tts"
	TypeWaterMark = "watermark"

	StatusCompleted     = "completed"  // 完成
	StatusProcessing    = "processing" // 处理中
	StatusIdle          = "idle"       // 异常退出
	StatusFailed        = "failed"
	WatermarkPresent    = "watermark present"
	WatermarkNotPresent = "watermark not present"
)

const (
	Success                 = 0    // 成功
	CodeSuccess             = 1000 // 成功
	CodeProcessing          = 1001 // 处理中
	CodeAbnormalExit        = 1002 // 空闲（异常退出）（可重试）
	CodeCompleted           = 1003 // 已完成
	CodeTaskNotFound        = 1004 // 任务未找到
	CodeInvalidParams       = 1005 // 参数错误
	CodeTaskFailed          = 1006 // 任务失败
	CodeWatermarkPresent    = 1007 // 有水印
	CodeWatermarkNotPresent = 1008 // 没有水印
	CodeAlgorithmBusy       = 1009 // 算法服务忙碌 无空闲资源处理该请求
)

type TTSRequest struct {
	Type             string         `json:"type"`
	UserID           int64          `json:"user_id"`
	TaskID           int64          `json:"task_id"`
	SourceUrl        string         `json:"source_url"`
	TargetUrl        string         `json:"target_url"`
	SourceText       string         `json:"source_text"`
	TargetText       string         `json:"target_text"`
	SourceLang       string         `json:"source_lang"`
	TargetLang       string         `json:"target_lang"`
	IsTrans          bool           `json:"is_trans"`     // true：视频翻译，false：声音克隆
	Speed            float32        `json:"speed"`        // 语速 0.5~1.5，默认1
	SpeakerName      string         `json:"speaker_name"` // 说话人名称
	VolumeNorm       int32          `json:"volume_norm"`  // 是否需要音量归一化 默认为不开启，1为开启，0为不开启
	TargetDuration   int64          `json:"target_duration,omitempty"`
	ExpectDuration   int64          `json:"expect_duration,omitempty"`    // 期望生成的音频时长，单位毫秒，非强制约束，只是给算法预测时长模块参考，实际生成可以超过此时长
	SpeakerAudioList []SpeakerAudio `json:"speaker_audio_list,omitempty"` // 说话人语音列表
	RegenerateNum    int32          `json:"regenerate_num,omitempty"`
}

type SpeakerAudio struct {
	Url   string  `json:"url"`          // 说话人音频文件的URL
	Score float64 `json:"score"`        // 评分
	ID    string  `json:"id,omitempty"` // 说话人ID，可选
}
type TTSResponse struct {
	Code          int                    `json:"code"`           // 1000:成功，1001:处理中，1003：已完成
	Message       string                 `json:"message"`        // processing, idle, completed, task_id mismatch
	FakeConfident float64                `json:"fake_confident"` // 0-1表示是否系统生成语音的概率， -1表示未知（水印校验接口才返回）
	WatermarkNum  int                    `json:"watermark_num"`  // 默认水印数字:17808（水印校验接口才返回）
	TaskID        int64                  `json:"task_id"`
	Duration      float64                `json:"duration"`  // 生成音频时长
	CostTime      float64                `json:"cost_time"` // 生成音频耗时
	ExtraData     map[string]interface{} `json:"extra_data"`
	Words         []*WordInfo            `json:"words"` // 分词结果
}

type TTSData struct {
	FakeConfident float64                `json:"fake_confident"` // 0-1表示是否系统生成语音的概率， -1表示未知（水印校验接口才返回）
	WatermarkNum  int                    `json:"watermark_num"`  // 默认水印数字:17808（水印校验接口才返回）
	TaskID        int64                  `json:"task_id"`
	Duration      float64                `json:"duration"` // 生成音频时长
	ExtraData     map[string]interface{} `json:"extra_data"`
	Words         []*WordInfo            `json:"words"` // 分词结果
}

type TTSWorker struct {
	*BaseWorker
	taskType               omniEngine.TaskType
	checkWatermarkUrl      string
	createTTSUrl           string
	checkTTSStatusUrl      string
	checkResultInterval    int // 检查结果的时间间隔（毫秒）
	maxRetryCount          int // 最大重试次数
	PollingIntervalSeconds int // 重新投递任务间隔
	PollingTimeOutSeconds  int // 重新投递超时时间
}

// 添加错误响应结构体
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Error   string `json:"error"`
}

func NewTTSWorker(taskType omniEngine.TaskType) *TTSWorker {
	cfg := config.GetConfig()
	return GetWorkerByTaskType(taskType, cfg)
}

func GetWorkerByTaskType(taskType omniEngine.TaskType, cfg *config.Config) *TTSWorker {
	ttsWorker := &TTSWorker{
		BaseWorker: NewBaseWorker(),
		taskType:   taskType,
	}
	switch taskType {
	case omniEngine.TaskType_TTS_SERVICE_PVC:
		ttsWorker.createTTSUrl = cfg.PVCTTSService.CreateUrl
		ttsWorker.checkTTSStatusUrl = cfg.PVCTTSService.ResultUrl
		checkResultInterval := cfg.PVCTTSService.CheckResultIntervalMs
		if checkResultInterval == 0 {
			checkResultInterval = 300
		}
		ttsWorker.checkResultInterval = checkResultInterval
		ttsWorker.maxRetryCount = cfg.PVCTTSService.MaxRetryCount // 设置最大重试次数
		ttsWorker.PollingIntervalSeconds = cfg.PVCTTSService.PollingIntervalSeconds
		ttsWorker.PollingTimeOutSeconds = cfg.PVCTTSService.PollingTimeOutSeconds
		if ttsWorker.PollingIntervalSeconds == 0 {
			ttsWorker.PollingIntervalSeconds = 1
		}
		if ttsWorker.PollingTimeOutSeconds == 0 {
			ttsWorker.PollingTimeOutSeconds = 3600
		}

		return ttsWorker
	case omniEngine.TaskType_TTS_SERVICE_MULTILANG:
		ttsWorker.createTTSUrl = cfg.TTSService.CreateTTSUrl
		ttsWorker.checkWatermarkUrl = cfg.TTSService.CheckWatermarkUrl
		ttsWorker.checkTTSStatusUrl = cfg.TTSService.CheckStatusUrl
		checkResultInterval := cfg.TTSService.CheckResultIntervalMs
		if checkResultInterval == 0 {
			checkResultInterval = 300
		}
		ttsWorker.checkResultInterval = checkResultInterval
		ttsWorker.maxRetryCount = cfg.TTSService.MaxRetryCount // 设置最大重试次数
		ttsWorker.PollingIntervalSeconds = cfg.TTSService.PollingIntervalSeconds
		ttsWorker.PollingTimeOutSeconds = cfg.TTSService.PollingTimeOutSeconds
		if ttsWorker.PollingIntervalSeconds == 0 {
			ttsWorker.PollingIntervalSeconds = 1
		}
		if ttsWorker.PollingTimeOutSeconds == 0 {
			ttsWorker.PollingTimeOutSeconds = 3600
		}
		return ttsWorker
	default:
		g.Log().Errorf(context.Background(), "Unsupported task type: %s", taskType)
		return ttsWorker
	}
}

func (w *TTSWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *TTSWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	ttsRequest := TTSRequest{
		Type:           t.GetTtsTaskInputContent().Type,
		UserID:         t.GetTenantId(),
		TaskID:         t.GetId(),
		SourceUrl:      t.GetTtsTaskInputContent().GetSourceUrl(),
		TargetUrl:      t.GetTtsTaskInputContent().GetTargetUrl(),
		SourceText:     t.GetTtsTaskInputContent().GetSourceText(),
		TargetText:     t.GetTtsTaskInputContent().GetTargetText(),
		SourceLang:     t.GetTtsTaskInputContent().GetSourceLang(),
		TargetLang:     t.GetTtsTaskInputContent().GetTargetLang(),
		IsTrans:        t.GetTtsTaskInputContent().GetIsTrans(),
		Speed:          t.GetTtsTaskInputContent().GetSpeed(),
		SpeakerName:    t.GetTtsTaskInputContent().GetSpeakerName(),
		VolumeNorm:     t.GetTtsTaskInputContent().GetVolumeNorm(),
		TargetDuration: t.GetTtsTaskInputContent().GetTargetDuration(),
		ExpectDuration: t.GetTtsTaskInputContent().GetExpectDuration(),
		SpeakerAudioList: lo.Map(t.GetTtsTaskInputContent().GetSpeakerAudioList(), func(item *omniEngine.SpeakerAudioInfo, _ int) SpeakerAudio {
			return SpeakerAudio{
				Url:   item.GetUrl(),
				Score: item.GetScore(),
			}
		}),
		RegenerateNum: t.GetTtsTaskInputContent().RegenerateNum,
	}
	// 查询任务前先睡500毫秒，等待算法将之前提交的任务存到内存
	time.Sleep(500 * time.Millisecond)
	return w.checkTaskStatus(ctx, ttsRequest)
}

func (w *TTSWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting TTS task: %d", t.Id)
	ttsRequest := TTSRequest{
		Type:           t.GetTtsTaskInputContent().Type,
		UserID:         t.GetTenantId(),
		TaskID:         t.GetId(),
		SourceUrl:      t.GetTtsTaskInputContent().GetSourceUrl(),
		TargetUrl:      t.GetTtsTaskInputContent().GetTargetUrl(),
		SourceText:     t.GetTtsTaskInputContent().GetSourceText(),
		TargetText:     t.GetTtsTaskInputContent().GetTargetText(),
		SourceLang:     t.GetTtsTaskInputContent().GetSourceLang(),
		TargetLang:     t.GetTtsTaskInputContent().GetTargetLang(),
		IsTrans:        t.GetTtsTaskInputContent().GetIsTrans(),
		Speed:          t.GetTtsTaskInputContent().GetSpeed(),
		SpeakerName:    t.GetTtsTaskInputContent().GetSpeakerName(),
		VolumeNorm:     t.GetTtsTaskInputContent().GetVolumeNorm(),
		TargetDuration: t.GetTtsTaskInputContent().GetTargetDuration(),
		ExpectDuration: t.GetTtsTaskInputContent().GetExpectDuration(),
		SpeakerAudioList: lo.Map(t.GetTtsTaskInputContent().GetSpeakerAudioList(), func(item *omniEngine.SpeakerAudioInfo, _ int) SpeakerAudio {
			return SpeakerAudio{
				Url:   item.GetUrl(),
				Score: item.GetScore(),
			}
		}),
		RegenerateNum: t.GetTtsTaskInputContent().RegenerateNum,
	}
	switch ttsRequest.Type {
	case TypeTTS:
		return w.handleTTSTask(ctx, ttsRequest, t.Type)
	case TypeWaterMark:
		return w.handleWatermarkTask(ctx, ttsRequest, t.Type)
	default:
		errMsg := fmt.Sprintf("Unsupported task type: %s", ttsRequest.Type)
		g.Log().Errorf(ctx, errMsg)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: errMsg,
		}
	}
}

func (w *TTSWorker) handleTTSTask(ctx context.Context, ttsRequest TTSRequest, taskType omniEngine.TaskType) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call tts request: %+v", ttsRequest)
	if err := w.validateTask(ttsRequest, taskType); err != nil {
		g.Log().Errorf(ctx, "Invalid TTS task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid TTS task: %v", err),
		}
	}
	return w.callTTSService(ctx, ttsRequest, w.createTTSUrl)
}

func (w *TTSWorker) handleWatermarkTask(ctx context.Context, watermarkRequest TTSRequest, taskType omniEngine.TaskType) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call watermark check request: %+v", watermarkRequest)
	if err := w.validateTask(watermarkRequest, taskType); err != nil {
		g.Log().Errorf(ctx, "Invalid watermark task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid watermark task: %v", err),
		}
	}
	watermarkResponse, err := w.callService(ctx, watermarkRequest, w.checkWatermarkUrl)
	if err != nil {
		g.Log().Errorf(ctx, "Watermark task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Watermark task failed: %v", err),
		}
	}

	g.Log().Infof(ctx, "Call watermark check response: %+v", watermarkResponse)
	if watermarkResponse.WatermarkNum == 17808 { // 17808:水印数字
		g.Log().Infof(ctx, "Watermark task %d completed successfully", watermarkRequest.TaskID)
		return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
			Code:    CodeWatermarkPresent,
			Message: WatermarkPresent,
		}
	} else {
		errorMsg := fmt.Sprintf("Watermark Task %d failed with watcher mark code: %d", watermarkRequest.TaskID, watermarkResponse.Code)
		g.Log().Errorf(ctx, errorMsg)
		return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
			Code:    CodeWatermarkNotPresent,
			Message: errorMsg,
		}
	}
}

func (w *TTSWorker) handleTaskResponse(ctx context.Context, request TTSRequest, response *TTSResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 1000:成功，1001:正在处理任务，1003:任务完成
	switch response.Code {
	case CodeCompleted:
		g.Log().Infof(ctx, "Task %d completed successfully", request.TaskID)
		return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
			Code:    CodeCompleted,
			Message: StatusCompleted,
		}

	case CodeSuccess, CodeProcessing:
		g.Log().Infof(ctx, "Task %d is still processing, checking status later", request.TaskID)
		return omniEngine.TaskStatus_IN_PROGRESS, nil
	case CodeAlgorithmBusy:
		g.Log().Infof(ctx, "The number of algorithm service processing tasks has reached the limit, taskID:%d", request.TaskID)
		return omniEngine.TaskStatus_NOT_STARTED, nil

	default:
		errorMsg := fmt.Sprintf("Task %d failed with code: %v  message: %s", request.TaskID, response.Code, response.Message)
		g.Log().Errorf(ctx, errorMsg)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: errorMsg,
		}
	}
}

func isValidUrl(rawUrl string) bool {
	_, err := url.ParseRequestURI(rawUrl)
	return err == nil
}

func (w *TTSWorker) validateTask(ttsRequest TTSRequest, taskType omniEngine.TaskType) error {
	if ttsRequest.Type == "tts" {
		// pvc任务不需要检查source_text和source_lang
		if taskType != omniEngine.TaskType_TTS_SERVICE_PVC {
			if !isValidUrl(ttsRequest.SourceUrl) {
				return errors.New("invalid source_url: must be a valid HTTP/HTTPS Url")
			}
			// 目前tts算法不需要source_text了
			// if ttsRequest.SourceText == "" {
			// 	return errors.New("source_text cannot be empty")
			// }
			if ttsRequest.SourceLang == "" {
				return errors.New("source_lang cannot be empty")
			}
		}
		if ttsRequest.TargetUrl == "" {
			return errors.New("target_url cannot be empty")
		}
		if ttsRequest.TargetLang == "" {
			return errors.New("target_lang cannot be empty")
		}
		if ttsRequest.TargetText == "" {
			return errors.New("target_text cannot be empty")
		}
	}
	return nil
}

func (w *TTSWorker) callService(ctx context.Context, ttsRequest TTSRequest, url string) (*TTSResponse, error) {
	g.Log().Infof(ctx, "sending tts request to Url: %s", url)
	ttsResponse := new(TTSResponse)
	err := w.PostJSON(ctx, url, ttsRequest, ttsResponse)
	if err != nil {
		errorMsg := fmt.Sprintf("create TTS task err: %s", err)
		g.Log().Errorf(ctx, errorMsg)
		return nil, err
	}

	return ttsResponse, nil
}

func (w *TTSWorker) callTTSService(ctx context.Context, ttsRequest TTSRequest, url string) (omniEngine.TaskStatus, *TaskOutputContent) {
	timeOutInterval := time.Duration(w.PollingTimeOutSeconds) * time.Second
	pollingInterval := time.Duration(w.PollingIntervalSeconds) * time.Second
	timeout := time.NewTimer(timeOutInterval)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			errorMsg := fmt.Sprintf("tts task submit timeout:%fs, taskId:%d", timeOutInterval.Seconds(), ttsRequest.TaskID)
			g.Log().Errorf(ctx, errorMsg)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: errorMsg,
			}
		default:
			g.Log().Infof(ctx, "sending tts request to Url: %s", url)
			ttsResponse := new(TTSResponse)
			err := w.PostJSON(ctx, url, ttsRequest, ttsResponse)
			if err != nil {
				errorMsg := fmt.Sprintf("create tts task err: %s", err)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}

			status, outputContent := w.handleTaskResponse(ctx, ttsRequest, ttsResponse)
			if status != omniEngine.TaskStatus_NOT_STARTED {
				return status, outputContent
			}
			time.Sleep(pollingInterval)
		}
	}
}

func (w *TTSWorker) retryTTSTask(ctx context.Context, ttsRequest TTSRequest, taskId int64) (bool, error) {
	// 重新提交任务
	retryResponse, err := w.callService(ctx, ttsRequest, w.createTTSUrl)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to retry TTS task: %v", err)
		return false, err
	}

	if retryResponse.Code == CodeSuccess {
		g.Log().Infof(ctx, "TTS task %d retry successful", taskId)
		return true, nil
	}
	if retryResponse.Code == CodeProcessing {
		g.Log().Infof(ctx, "TTS task %d is still processing, checking status later", taskId)
		return false, nil
	}

	g.Log().Errorf(ctx, "TTS task %d retry failed with code: %d", taskId, retryResponse.Code)
	return false, nil
}

func (w *TTSWorker) checkTaskStatus(ctx context.Context, ttsRequest TTSRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	taskId := ttsRequest.TaskID
	statusRequest := map[string]interface{}{
		"task_id": taskId,
	}
	checkResultTicker := time.NewTicker(time.Duration(w.checkResultInterval) * time.Millisecond)
	defer checkResultTicker.Stop()

	timeOutInterval := time.Duration(w.PollingTimeOutSeconds) * time.Second
	timeout := time.NewTimer(timeOutInterval)
	defer timeout.Stop()
	retryCount := 0
	for {
		select {
		case <-timeout.C:
			errorMsg := fmt.Sprintf("tts task check status timeout:%d, taskId:%d", timeOutInterval, ttsRequest.TaskID)
			g.Log().Errorf(ctx, errorMsg)
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: errorMsg,
			}
		default:
			resp, err := w.GetCheckResultByTaskType(ctx, w.taskType, statusRequest)
			if err != nil {
				g.Log().Errorf(ctx, "Failed to call TTS status check service: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Failed to call TTS status check service: %v", err),
				}
			}
			if resp.StatusCode() != 200 {
				g.Log().Errorf(ctx, "Status check service returned non-200 status: %v", resp.Status())
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Status check service returned non-200 status: %v", resp.Status()),
				}
			}
			statusResponse := resp.Result().(*TTSResponse)
			g.Log().Infof(ctx, "Status check response: %+v", statusResponse)
			switch statusResponse.Code {
			case CodeCompleted:
				return w.handleCompletedStatus(ctx, taskId, statusResponse)
			case CodeProcessing:
				g.Log().Infof(ctx, "TTS task %d is still processing", taskId)
			case CodeAbnormalExit:
				if retryCount < w.maxRetryCount {
					retryCount++
					g.Log().Warningf(ctx, "TTS task %d is AbnormalExit, retrying... (attempt %d/%d), response: %+v",
						taskId, retryCount, w.maxRetryCount, statusResponse)
					_, err := w.retryTTSTask(ctx, ttsRequest, taskId)
					if err != nil {
						continue
					}
				} else {
					g.Log().Errorf(ctx, "TTS task %d exceeded maximum retry attempts", taskId)
					// 记录重试达到限制的告警
					monitor.RecordRetryLimitReached(w.taskType)
					return w.handleFailedStatus(ctx, taskId, statusResponse)
				}
			case CodeTaskNotFound:
				g.Log().Debugf(ctx, "checkTaskStatus task not found, taskId:%d", taskId)
				return w.handleFailedStatus(ctx, taskId, statusResponse)
			default:
				return w.handleFailedStatus(ctx, taskId, statusResponse)
			}

		}
	}
}

func (w *TTSWorker) handleCompletedStatus(ctx context.Context, taskID int64, statusResponse *TTSResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	if statusResponse.Code == CodeCompleted {
		g.Log().Infof(ctx, "TTS task %d completed", taskID)
		taskOutputContent := ConvTTSResp2TaskOutContent(statusResponse)
		return omniEngine.TaskStatus_COMPLETED, taskOutputContent
	}
	return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
		Code:    CodeTaskFailed,
		Message: fmt.Sprintf("unexpected message: %v", statusResponse.Message),
	}
}

func ConvTTSResp2TaskOutContent(statusResponse *TTSResponse) *TaskOutputContent {
	if statusResponse == nil {
		return nil
	}

	taskOutputContent := &TaskOutputContent{
		Code:    statusResponse.Code,
		Message: statusResponse.Message,
		Data:    statusResponse,
	}
	return taskOutputContent
}

func (w *TTSWorker) handleFailedStatus(ctx context.Context, taskID int64, statusResponse *TTSResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	errorMsg := fmt.Sprintf("TTS task %d failed with code %d", taskID, statusResponse.Code)
	g.Log().Errorf(ctx, errorMsg)
	taskOutputContent := ConvTTSResp2TaskOutContent(statusResponse)
	return omniEngine.TaskStatus_FAILED, taskOutputContent
}

func (w *TTSWorker) GetCheckResultByTaskType(ctx context.Context, taskType omniEngine.TaskType, statusRequest map[string]interface{}) (*resty.Response, error) {
	switch taskType {
	case omniEngine.TaskType_TTS_SERVICE_PVC:
		return w.GetPVCTTSResult(ctx, statusRequest)
	case omniEngine.TaskType_TTS_SERVICE_MULTILANG:
		return w.GetTTSResult(ctx, statusRequest)
	default:
		return nil, errors.New("unsupported task type")
	}
}

func (w *TTSWorker) GetPVCTTSResult(ctx context.Context, statusRequest map[string]interface{}) (*resty.Response, error) {
	return w.restClient.R(ctx).
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(statusRequest).
		SetResult(&TTSResponse{}).
		Post(w.checkTTSStatusUrl)
}

func (w *TTSWorker) GetTTSResult(ctx context.Context, statusRequest map[string]interface{}) (*resty.Response, error) {
	return w.restClient.R(ctx).
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(statusRequest).
		SetResult(&TTSResponse{}).
		Post(w.checkTTSStatusUrl)
}
