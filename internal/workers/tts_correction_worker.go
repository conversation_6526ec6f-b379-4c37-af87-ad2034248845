package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type TTSCorrectionReq struct {
	UserId    int64             `json:"user_id"`
	TaskId    int64             `json:"task_id"`
	SourceUrl string            `json:"source_url"` // 纯人声url
	OCRResult []*CorrectionData `json:"ocr_result"`
	ASRResult []*CorrectionData `json:"asr_result"`
	DiaInfos  []*DiaInfo        `json:"dia_infos"`
}

type DiaInfo struct {
	Start   float64 `json:"start"`
	End     float64 `json:"end"`
	Speaker string  `json:"speaker"`
	Index   string  `json:"index"`
}

type TTSCorrectionRes struct {
	Code              int               `json:"code"`
	Msg               string            `json:"message"`
	AudioSegmentation []*CorrectionData `json:"audio_segmentation"`
}

type TTSCorrectionWorker struct {
	*BaseWorker
	CreateUrl string
	taskType  omniEngine.TaskType
}

func NewTTSCorrectionWorker(taskType omniEngine.TaskType) *TTSCorrectionWorker {
	cfg := config.GetConfig()
	return &TTSCorrectionWorker{
		BaseWorker: NewBaseWorker(),
		CreateUrl:  cfg.VideoTranslateService.TTSCorrectionCreateUrl,
		taskType:   taskType,
	}
}

func (w *TTSCorrectionWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *TTSCorrectionWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *TTSCorrectionWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	IdStr := strconv.FormatInt(t.Id, 10)
	ctx = context.WithValue(ctx, consts.ContextKeyEngineTaskId, gctx.StrKey(IdStr))
	req := &TTSCorrectionReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "TTSCorrectionWorker unmarshal failed, err:%s, taskId:%d, content: %s", err, t.Id, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	g.Log().Infof(ctx, "TTSCorrectionWorker start, taskId:%d, req:%+v", t.Id, *req)
	return w.doCreateAudioSplitCorrection(ctx, t.Id, req)
}

func (w *TTSCorrectionWorker) doCreateAudioSplitCorrection(ctx context.Context, taskId int64, req *TTSCorrectionReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 构建查询请求
	resp := &TTSCorrectionRes{}
	beginTime := time.Now()

	err := retry.Do(func() error {
		timeoutCtx, cancel := context.WithTimeout(ctx, 15*time.Minute)
		defer cancel()
		return w.PostJSON(timeoutCtx, w.CreateUrl, req, resp)
	},
		retry.Attempts(3),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    0,
			Message: fmt.Sprintf("audio split correction failed:%v", err),
		}
	}
	// 打印耗时
	g.Log().Infof(ctx, "TTSCorrectionWorker create tts correction, taskId: %v, cost:%vms", taskId, time.Since(beginTime).Milliseconds())
	if resp.Code != CodeSuccess {
		g.Log().Errorf(ctx, "TTSCorrectionWorker create tts correction failed, req:%+v, code:%d", *req, resp.Code)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    resp.Code,
			Message: fmt.Sprintf("audio split correction failed,req:%+v, code:%d", *req, resp.Code),
		}
	}
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    0,
		Message: "success",
		Data:    resp,
	}
}
