package workers

import (
	"context"
	"testing"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

func Test_CommentaryQiFeiWorker(t *testing.T) {
	PatchConvey("Test_CommentaryQiFeiWorker_Create", t, func() {
		worker := &CommentaryQiFeiWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_COMMENTARY_QIFEI,
			CreateUrl:  "http://aigc.cn-bj.rcmd-tt.skyengine.net.cn/api-workflow/release/3568/3568zfOfXbacCstart",
			AccessKey:  "148:d5bb5d8aab477344759bde136318ed31",
		}
		status, taskContent := worker.handleCommentaryTask(context.Background(), &omniEngine.Task{Id: 123}, &CommentaryQiFeiReq{
			Urls:           "https://cdn-allvoice-down-cn-testing.funnycp.com/allvoice/test/private/video_trans/2572715391994315341/20250829/e14c1edf-d3c8-470b-8bfc-76d6c0ab8112.mp4",
			OriginLanguage: "zh",
			Timeline:       30,
		})
		So(status, ShouldEqual, omniEngine.TaskStatus_IN_PROGRESS)
		So(taskContent.Data.(*CommentaryQiFeiResp).Data, ShouldNotBeNil)
	})
}
