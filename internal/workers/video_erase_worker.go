package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type EraseV2Req struct {
	Id           int64     `json:"id"`
	ChunkIdx     int32     `json:"chunk_idx"`
	TotalChunk   int32     `json:"total_chunk"`
	FramesRange  []int32   `json:"frames_range"`
	ChunkBbox    []int32   `json:"chunk_bbox"`
	InferBbox    [][]int32 `json:"infer_bbox"`
	ChunkPath    string    `json:"chunk_path"`
	InferSize    []int32   `json:"infer_size"`
	MasterFrames []int32   `json:"master_frames"`
	OccupiedSize float32   `json:"occupied_size"`
}

type EraseV2Res struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

type EraseV2QueryReq struct {
	Id      int64 `json:"id"`
	ChunkId int32 `json:"chunk_idx"`
}

type EraseV2QueryRes struct {
	Id            int64   `json:"id"`
	ChunkIdx      int32   `json:"chunk_idx"`
	Code          int32   `json:"code"`
	ProgressRatio float64 `json:"progress_ratio"`
	Msg           string  `json:"message"`
}

type EraseV2Worker struct {
	*BaseWorker
	EraseV2CreateUrl string
	EraseV2QueryUrl  string
	taskType         omniEngine.TaskType
}

func NewEraseV2Worker(taskType omniEngine.TaskType) *EraseV2Worker {
	cfg := config.GetConfig()
	return &EraseV2Worker{
		BaseWorker:       NewBaseWorker(),
		EraseV2CreateUrl: cfg.VideoTranslateService.EraseV2CreateUrl,
		EraseV2QueryUrl:  cfg.VideoTranslateService.EraseV2QueryUrl,
		taskType:         taskType,
	}
}

func (w *EraseV2Worker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *EraseV2Worker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &EraseV2Req{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "EraseV2Worker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.query(ctx, t, req)
}

func (w *EraseV2Worker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &EraseV2Req{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "EraseV2Worker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleEraseV2Task(ctx, t, req)
}

func (w *EraseV2Worker) handleEraseV2Task(ctx context.Context, t *omniEngine.Task, req *EraseV2Req) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleEraseV2Task] start task, engineId: %v, taskId: %v", t.Id, req.Id)
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[handleEraseV2Task] timeout, engineId: %v, taskId: %v", t.Id, req.Id)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "task submit timeout",
			}
		default:
			status, res := w.submitTask(ctx, t, req)
			if status == omniEngine.TaskStatus_NOT_STARTED {
				time.Sleep(time.Second)
			} else {
				return status, res
			}
		}
	}
}

func (w *EraseV2Worker) submitTask(ctx context.Context, t *omniEngine.Task, req *EraseV2Req) (omniEngine.TaskStatus, *TaskOutputContent) {
	res := &EraseV2Res{}
	err := retry.Do(func() error {
		return w.PostJSON(ctx, w.EraseV2CreateUrl, req, res)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "[submitTask] HTTP req failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("http req failed:%s", err),
		}
	}
	switch res.Code {
	case consts.AiCodeOk:
		g.Log().Infof(ctx, "[submitTask] submit ok, engineId: %v, taskId: %v", t.Id, req.Id)
		return omniEngine.TaskStatus_IN_PROGRESS, &TaskOutputContent{
			Code:    consts.CommonCodeOk,
			Message: "ok",
			Data:    res,
		}
	case consts.AiCodeBusy:
		// 预处理繁忙，继续尝试提交
		g.Log().Infof(ctx, "[submitTask] erase busy, retry commit, msg:%s, id: %v, engineId: %v", res.Msg, req.Id, t.Id)
		return omniEngine.TaskStatus_NOT_STARTED, nil
	case consts.AiCodeParamsErr:
		g.Log().Errorf(ctx, "[submitTask] task failed, err:%s, content: %+v", res.Msg, req)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("task failed:%s", res.Msg),
			Data:    res,
		}
	default:
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("unknown code:%d", res.Code),
			Data:    res,
		}
	}
}

func (w *EraseV2Worker) query(ctx context.Context, t *omniEngine.Task, req *EraseV2Req) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 构建查询请求
	queryReq := &EraseV2QueryReq{Id: req.Id, ChunkId: req.ChunkIdx}
	resp := &EraseV2QueryRes{}
	beginTime := time.Now()
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	retryCount := 0
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[query] timeout, engineId: %v, taskId: %v, beginTime: %v",
				t.Id, req.Id, beginTime.Format(time.DateTime))
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "query result timeout",
			}
		default:
			err := retry.Do(func() error {
				return w.PostJSON(ctx, w.EraseV2QueryUrl, queryReq, resp)
			},
				retry.Attempts(5),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "[query] http request failed: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("http request failed:%s", err),
				}
			}
			switch resp.Code {
			case consts.AiCodeOk:
				g.Log().Infof(ctx, "[query] task completed, engineId: %v, taskId: %v, timeCost: %v",
					t.Id, req.Id, time.Since(beginTime).Seconds())
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    consts.CommonCodeOk,
					Message: "ok",
					Data:    resp,
				}
			case consts.AiCodeBusy:
				g.Log().Infof(ctx, "[query] erase in progress, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				time.Sleep(time.Second)
			case consts.AiCodeTaskNotExist, consts.AiCodeCudaOutOfMemory:
				g.Log().Infof(ctx, "[query] retry submit task, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				retryCount++
				if retryCount > 3 {
					return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
						Code:    consts.CommonCodeFail,
						Message: "attempt all retry failed",
					}
				}
				// 任务不存在或者内存不足，需要重新提交
				status, res := w.handleEraseV2Task(ctx, t, req)
				// 重新提交成功，继续查询结果
				if status == omniEngine.TaskStatus_IN_PROGRESS {
					continue
				}
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: "retry submit task failed",
					Data:    res,
				}
			case consts.AiCodePullVideoFailed, consts.AiCodeParamsErr,
				consts.AiCodeInternalError:
				g.Log().Infof(ctx, "[query] task failed, taskId: %v, engineId: %v, code: %v, msg: %s",
					req.Id, t.Id, resp.Code, resp.Msg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: resp.Msg,
					Data:    resp,
				}
			default:
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("unknown code:%d", resp.Code),
					Data:    resp,
				}
			}
		}
	}
}
