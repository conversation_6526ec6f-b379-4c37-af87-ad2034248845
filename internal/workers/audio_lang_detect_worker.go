package workers

import (
	"context"
	"errors"
	"fmt"

	"omni_worker/internal/util"
	"omni_worker/pkg/config"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type AudioLangDetectRequest struct {
	UserID   int64  `json:"user_id,omitempty"`
	TaskID   int64  `json:"task_id,omitempty"`
	AudioUrl string `json:"audio_url,omitempty"`
}

type AudioLangDetectData struct {
	Lang string `json:"lang"`
}

type AudioLangDetectResponse struct {
	Code           int                 `json:"code"` // 0: 成功，1: 失败
	Message        string              `json:"message"`
	UserID         int                 `json:"user_id"`
	TaskID         int64               `json:"task_id"`
	LangDetectData AudioLangDetectData `json:"data"`
}

type AudioLangDetectWorker struct {
	*BaseWorker
	createUrl string
	taskType  omniEngine.TaskType
}

func NewAudioLangDetectWorker(taskType omniEngine.TaskType) *AudioLangDetectWorker {
	cfg := config.GetConfig()
	return &AudioLangDetectWorker{
		BaseWorker: NewBaseWorker(),
		createUrl:  cfg.AudioLangDetectServiceConfig.CreateUrl,
		taskType:   taskType,
	}
}

func (a *AudioLangDetectWorker) GetWorkerName() string {
	return config.GetTaskNameByType(a.taskType)
}

func (a *AudioLangDetectWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (a *AudioLangDetectWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting AUDIO_LANG_DETECT task: %d", t.Id)

	request := AudioLangDetectRequest{
		UserID:   t.TenantId,
		TaskID:   t.Id,
		AudioUrl: t.CommonInputContent,
	}
	return a.handleLangDetectTask(ctx, request)
}

func (a *AudioLangDetectWorker) handleLangDetectTask(ctx context.Context, request AudioLangDetectRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call LANG_DETECT request: %+v", util.ToJson(request))
	if err := a.validateTask(request); err != nil {
		g.Log().Errorf(ctx, "Invalid AUDIO_LANG_DETECT task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid AUDIO_LANG_DETECT task: %v", err),
		}
	}

	var response AudioLangDetectResponse
	if err := a.PostJSON(ctx, a.createUrl, request, &response); err != nil {
		g.Log().Errorf(ctx, "AUDIO_LANG_DETECT task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("AUDIO_LANG_DETECT task failed: %v", err),
		}
	}
	g.Log().Infof(ctx, "Call AUDIO_LANG_DETECT response: %+v", util.ToJson(response))
	return a.handleTaskResponse(ctx, request, &response)
}

func (a *AudioLangDetectWorker) validateTask(request AudioLangDetectRequest) error {
	if !isValidUrl(request.AudioUrl) {
		return errors.New("invalid source_url: must be a valid HTTP/HTTPS Url")
	}
	if request.AudioUrl == "" {
		return errors.New("source_url cannot be empty")
	}
	return nil
}

func (a *AudioLangDetectWorker) handleTaskResponse(ctx context.Context, request AudioLangDetectRequest, response *AudioLangDetectResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 0: 成功，1: 失败
	switch response.Code {
	case Success:
		g.Log().Infof(ctx, "LANG_DETECT task %d completed successfully", request.TaskID)
		taskOutputContent := &TaskOutputContent{
			Code:    CodeCompleted,
			Message: StatusCompleted,
			Data:    response.LangDetectData,
		}
		return omniEngine.TaskStatus_COMPLETED, taskOutputContent
	default:
		errorMsg := fmt.Sprintf("LANG_DETECT task %d failed with code: %v  message: %s", request.TaskID, response.Code, response.Message)
		g.Log().Errorf(ctx, errorMsg)
		taskOutputContent := &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: errorMsg,
		}
		return omniEngine.TaskStatus_FAILED, taskOutputContent
	}
}
