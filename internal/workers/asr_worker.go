package workers

import (
	"context"
	"errors"
	"fmt"

	"omni_worker/pkg/config"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type ASRRequest struct {
	UserID     int64  `json:"user_id"`
	TaskID     int64  `json:"task_id"`
	SourceUrl  string `json:"source_url"`
	SourceLang string `json:"source_lang"`
	SourceText string `json:"source_text"`
}

type ASRData struct {
	SourceLang   string  `json:"source_lang"`
	VocalUrl     string  `json:"vocal_url"`
	Text         string  `json:"text"`
	MatchingRate float64 `json:"matching_rate"`
}

type ASRResponse struct {
	Code    int     `json:"code"` // 0: 成功，1: 失败
	Message string  `json:"message"`
	UserID  int     `json:"user_id"`
	TaskID  int64   `json:"task_id"`
	ASRData ASRData `json:"data"`
}

type ASRWorker struct {
	*BaseWorker
	createASRUrl string
	taskType     omniEngine.TaskType
}

func NewASRWorker(taskType omniEngine.TaskType) *ASRWorker {
	cfg := config.GetConfig()
	return &ASRWorker{
		BaseWorker:   NewBaseWorker(),
		createASRUrl: cfg.ASRService.CreateUrl,
		taskType:     taskType,
	}
}

func (w *ASRWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *ASRWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (a *ASRWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting ASR task: %d", t.Id)

	request := ASRRequest{
		UserID:     t.TenantId,
		TaskID:     t.Id,
		SourceUrl:  t.AsrTaskInputContent.SourceUrl,
		SourceLang: t.AsrTaskInputContent.SourceLang,
		SourceText: t.AsrTaskInputContent.SourceText,
	}
	return a.handleASRTask(ctx, request)
}

func (a *ASRWorker) handleASRTask(ctx context.Context, request ASRRequest) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Call ARS request: %+v", request)
	if err := a.validateTask(request); err != nil {
		g.Log().Errorf(ctx, "Invalid ASR task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Invalid ASR task: %v", err),
		}
	}

	var response ASRResponse
	if err := a.PostJSON(ctx, a.createASRUrl, request, &response); err != nil {
		g.Log().Errorf(ctx, "ASR task failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("ASR task failed: %v", err),
		}
	}
	g.Log().Infof(ctx, "Call ARS response: %+v", response)
	return a.handleTaskResponse(ctx, request, &response)
}

func (a *ASRWorker) validateTask(request ASRRequest) error {
	if !isValidUrl(request.SourceUrl) {
		return errors.New("invalid source_url: must be a valid HTTP/HTTPS Url")
	}
	if request.SourceLang == "" {
		return errors.New("source_lang cannot be empty")
	}
	return nil
}

func (a *ASRWorker) handleTaskResponse(ctx context.Context, request ASRRequest, response *ASRResponse) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 0: 成功，1: 失败
	switch response.Code {
	case Success:
		g.Log().Infof(ctx, "ASR task %d completed successfully", request.TaskID)
		taskOutputContent := &TaskOutputContent{
			Code:    CodeCompleted,
			Message: StatusCompleted,
			Data:    response.ASRData,
		}
		return omniEngine.TaskStatus_COMPLETED, taskOutputContent
	default:
		errorMsg := fmt.Sprintf("ASR task %d failed with code: %v  message: %s", request.TaskID, response.Code, response.Message)
		g.Log().Errorf(ctx, errorMsg)
		taskOutputContent := &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: errorMsg,
		}
		return omniEngine.TaskStatus_FAILED, taskOutputContent
	}
}
