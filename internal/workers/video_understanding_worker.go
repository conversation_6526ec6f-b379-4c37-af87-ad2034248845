package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/pkg/config"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type VideoUnderstandingReq struct {
	TaskID   int64  `json:"task_id"`
	VideoURL string `json:"video_url"`
}

type VideoUnderstandingRes struct {
	Code    int                     `json:"code"`
	Message string                  `json:"message"`
	Data    *VideoUnderstandingData `json:"data"`
}

type VideoUnderstandingData struct {
	TaskID        int64  `json:"task_id"`
	Understanding string `json:"understanding"`
}

type VideoUnderstandingWorker struct {
	*BaseWorker
	taskType omniEngine.TaskType
}

func NewVideoUnderstandingWorker(taskType omniEngine.TaskType) *VideoUnderstandingWorker {
	return &VideoUnderstandingWorker{
		BaseWorker: NewBaseWorker(),
		taskType:   taskType,
	}
}

func (w *VideoUnderstandingWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *VideoUnderstandingWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *VideoUnderstandingWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &VideoUnderstandingReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "VideoUnderstandingWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleVideoUnderstandingTask(ctx, req)
}

func (w *VideoUnderstandingWorker) handleVideoUnderstandingTask(ctx context.Context, req *VideoUnderstandingReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleVideoUnderstandingTask] start task, engineId: %v", req.TaskID)
	cfg := config.GetConfig()
	timeoutInterval := time.Duration(cfg.VideoUnderstandingConfig.TimeoutSeconds) * time.Second
	submitTaskInterval := time.Duration(cfg.VideoUnderstandingConfig.SubmitTaskIntervalMs) * time.Millisecond
	timeout := time.NewTimer(timeoutInterval)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			errorMsg := fmt.Sprintf("task submit timeout:%fs, taskId:%d", timeoutInterval.Seconds(), req.TaskID)
			g.Log().Errorf(ctx, errorMsg)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: errorMsg,
			}
		default:
			resp := &VideoUnderstandingRes{}
			err := retry.Do(func() error {
				return w.PostJSON(ctx, cfg.VideoUnderstandingConfig.CreateUrl, req, resp)
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				errorMsg := fmt.Sprintf("create task err: %s", err)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
			switch resp.Code {
			case CodeSuccess:
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    CodeSuccess,
					Message: resp.Message,
					Data:    resp,
				}
			case CodeAlgorithmBusy:
				time.Sleep(submitTaskInterval)
			default:
				errorMsg := fmt.Sprintf("create task err, code: %d, message: %s", resp.Code, resp.Message)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
		}
	}
}
