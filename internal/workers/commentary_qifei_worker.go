package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type CommentaryQiFeiWorker struct {
	*BaseWorker
	CreateUrl string
	AccessKey string
	taskType  omniEngine.TaskType
}

type CommentaryQiFeiReq struct {
	Urls           string `json:"urls"`
	OriginLanguage string `json:"origin_language"` // 原语言，现在仅支持 zh
	Timeline       int    `json:"timeline"`        // 生成的解说长度预期值，单位秒
}

type CommentaryQiFeiResp struct {
	Data      *CommentaryQiFeiRespData `json:"data"`
	RequestID string                   `json:"request_id"`
}

type CommentaryQiFeiRespData struct {
	Result *CommentaryQiFeiResult `json:"result"`
}

type createCommentaryQiFeiReq struct {
	OriginLanguage string `json:"origin_language"`
	Timeline       string `json:"timeline"`
	UrlStrings     string `json:"url_strings"`
}

type createCommentaryQiFeiResp struct {
	Originalout string `json:"originalout"`
	RequestID   string `json:"request_id"`
}

type CommentaryQiFeiResult struct {
	TimeFrameRange []*CommentaryQiFeiTimeFrame `json:"time_frame_range"`
	TotalContext   string                      `json:"total_context"`
}

type CommentaryQiFeiTimeFrame struct {
	Episode        string `json:"episode"`
	Context        string `json:"context"`
	EndTimestamp   string `json:"end_timestamp"`
	StartTimestamp string `json:"start_timestamp"`
}

func NewCommentaryQiFeiWorker(taskType omniEngine.TaskType) *CommentaryQiFeiWorker {
	cfg := config.GetConfig()
	return &CommentaryQiFeiWorker{
		BaseWorker: NewBaseWorker(),
		CreateUrl:  cfg.CommentaryQiFeiConfig.CreateUrl,
		AccessKey:  cfg.CommentaryQiFeiConfig.AccessKey,
		taskType:   taskType,
	}
}

func (w *CommentaryQiFeiWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *CommentaryQiFeiWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *CommentaryQiFeiWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &CommentaryQiFeiReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "CommentaryQiFeiWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleCommentaryTask(ctx, t, req)
}

func (w *CommentaryQiFeiWorker) handleCommentaryTask(ctx context.Context, t *omniEngine.Task, req *CommentaryQiFeiReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleCommentaryTask] start task, engineId: %v", t.Id)
	createReq := &createCommentaryQiFeiReq{
		OriginLanguage: req.OriginLanguage,
		Timeline:       strconv.Itoa(req.Timeline),
		UrlStrings:     req.Urls,
	}
	createResp := &createCommentaryQiFeiResp{}
	timeoutCtx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()
	var resp *CommentaryQiFeiResp
	err := retry.Do(func() error {
		httpResp, err := w.restClient.R(timeoutCtx).
			SetContext(timeoutCtx).
			SetHeader("Content-Type", "application/json").
			SetHeader("Authorization", w.AccessKey).
			SetHeader(consts.TaskContextKeyCluster, GetClusterFromContext(ctx)). // 添加集群信息，用于算法判断使用oss client
			SetBody(createReq).
			SetResult(createResp).
			Post(w.CreateUrl)
		if err != nil {
			g.Log().Errorf(ctx, "[handleCommentaryTask] http request failed: %v", err)
			return err
		}
		if httpResp.StatusCode() != 200 {
			g.Log().Errorf(ctx, "[handleCommentaryTask] http resp status code not 200, resp: %s", httpResp.String())
			return fmt.Errorf("unexpected status code: %d", httpResp.StatusCode())
		}
		resp, err = w.parseQiFeiResp(ctx, createResp)
		if err != nil {
			g.Log().Errorf(ctx, "[handleCommentaryTask] parse resp failed: %v", err)
			return err
		}
		return nil
	},
		retry.Attempts(3),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("http req failed:%s", err),
		}
	}
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    CodeSuccess,
		Message: "success",
		Data:    resp,
	}
}

func (w *CommentaryQiFeiWorker) parseQiFeiResp(ctx context.Context, createResp *createCommentaryQiFeiResp) (*CommentaryQiFeiResp, error) {
	createRespStr, _ := json.Marshal(createResp)
	g.Log().Infof(ctx, "[parseQiFeiResp] createRespStr: %s", createRespStr)
	if len(createResp.Originalout) == 0 {
		return nil, fmt.Errorf("originalout is empty")
	}
	// 找到第一个"{"和最后一个"}"的索引
	firstIndex := strings.Index(createResp.Originalout, "{")
	lastIndex := strings.LastIndex(createResp.Originalout, "}")
	if firstIndex == -1 || lastIndex == -1 || firstIndex >= lastIndex {
		return nil, fmt.Errorf("invalid json format")
	}
	jsonStr := createResp.Originalout[firstIndex : lastIndex+1]
	var result CommentaryQiFeiResult
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal failed, err:%s, jsonStr: %s", err, jsonStr)
		return nil, fmt.Errorf("unmarshal failed")
	}
	return &CommentaryQiFeiResp{
		RequestID: createResp.RequestID,
		Data: &CommentaryQiFeiRespData{
			Result: &result,
		},
	}, nil
}
