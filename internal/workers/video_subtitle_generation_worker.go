package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/pkg/config"

	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
)

type VideoSubtitleGenerationReq struct {
	TaskID              int64   `json:"task_id"`
	Style               string  `json:"style"`                // 解说风格，可选
	ExampleText         string  `json:"example_text"`         // 示例文本，可选
	TargetLang          string  `json:"target_lang"`          // 目标语言，可选
	UnderstandingResult string  `json:"understanding_result"` // 视频理解结果
	IsFirstPerson       bool    `json:"is_first_person"`      // 是否为第一人称解说，默认false：第三人称
	GenTime             float64 `json:"gen_time"`             // 生成时间，单位秒
}

type VideoSubtitleGenerationRes struct {
	Code    int                          `json:"code"`
	Message string                       `json:"message"`
	Data    *VideoSubtitleGenerationData `json:"data"`
}

type VideoSubtitleGenerationData struct {
	TaskID         int64            `json:"task_id"`
	ScriptSegments []*ScriptSegment `json:"script_segments"`
}

type ScriptSegment struct {
	SegmentID       int64  `json:"segment_id"`
	NarrationScript string `json:"narration_script"`
}

type VideoSubtitleGeneration struct {
	*BaseWorker
	VideoSubtitleGenerationCreateUrl string
	VideoSubtitleGenerationQueryUrl  string
	taskType                         omniEngine.TaskType
}

func NewVideoSubtitleGenerationWorker(taskType omniEngine.TaskType) *VideoSubtitleGeneration {
	cfg := config.GetConfig()
	return &VideoSubtitleGeneration{
		BaseWorker:                       NewBaseWorker(),
		VideoSubtitleGenerationCreateUrl: cfg.VideoTranslateService.EraseV2CreateUrl,
		VideoSubtitleGenerationQueryUrl:  cfg.VideoTranslateService.EraseV2QueryUrl,
		taskType:                         taskType,
	}
}

func (w *VideoSubtitleGeneration) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *VideoSubtitleGeneration) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *VideoSubtitleGeneration) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &VideoSubtitleGenerationReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "VideoSubtitleGeneration unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleVideoSubtitleGenerationTask(ctx, req)
}

func (w *VideoSubtitleGeneration) handleVideoSubtitleGenerationTask(ctx context.Context, req *VideoSubtitleGenerationReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleVideoSubtitleGenerationTask] start task, engineId: %v", req.TaskID)
	cfg := config.GetConfig()
	timeoutInterval := time.Duration(cfg.VideoSubtitleGenerationConfig.TimeoutSeconds) * time.Second
	submitTaskInterval := time.Duration(cfg.VideoSubtitleGenerationConfig.SubmitTaskIntervalMs) * time.Millisecond
	timeout := time.NewTimer(timeoutInterval)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			errorMsg := fmt.Sprintf("task submit timeout:%fs, taskId:%d", timeoutInterval.Seconds(), req.TaskID)
			g.Log().Errorf(ctx, errorMsg)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: errorMsg,
			}
		default:
			resp := &VideoSubtitleGenerationRes{}
			err := retry.Do(func() error {
				return w.PostJSON(ctx, cfg.VideoSubtitleGenerationConfig.CreateUrl, req, resp)
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				errorMsg := fmt.Sprintf("create task err: %s", err)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
			switch resp.Code {
			case CodeSuccess:
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    CodeSuccess,
					Message: resp.Message,
					Data:    resp,
				}
			case CodeAlgorithmBusy:
				time.Sleep(submitTaskInterval)
			default:
				errorMsg := fmt.Sprintf("create task err, code: %d, message: %s", resp.Code, resp.Message)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
		}
	}
}
