package workers

import (
	"fmt"

	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type WorkerFactory struct{}

func NewWorkerFactory() *WorkerFactory {
	return &WorkerFactory{}
}

// 在 CreateWorker 方法中添加对 ThirdPartyTTSWorker 的支持
func (f *WorkerFactory) CreateWorker(taskType omniEngine.TaskType) (Worker, error) {
	switch taskType {
	case omniEngine.TaskType_TTS_SERVICE_MULTILANG,
		omniEngine.TaskType_TTS_SERVICE_PVC:
		return NewTTSWorker(taskType), nil
	// 添加对泼墨体TTS的支持
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_MINIMAX:
		return NewThirdPartyTTSWorker(taskType), nil
	case omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_11LABS:
		return NewThirdPartyTTSWorker(taskType), nil
	case omniEngine.TaskType_AUDIO_SEPARATE:
		return NewAudioSeparateWorker(taskType), nil
	case omniEngine.TaskType_ASR_SERVICE:
		return NewASRWorker(taskType), nil
	case omniEngine.TaskType_STS_SERVICE:
		return NewSTSWorker(taskType), nil
	case omniEngine.TaskType_VOICE_ISOLATE_SERVICE:
		return NewVoiceIsolateWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_ERASE:
		return NewEraseV2Worker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_OCR_EXTRACT:
		return NewExtractV2Worker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_POST_MERGE:
		return NewPostMergeWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS:
		return NewSubtitlePreprocessWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_V2_ASR_CORRECT:
		return NewASRCorrectionWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_V2_TTS_CORRECT:
		return NewTTSCorrectionWorker(taskType), nil
	case omniEngine.TaskType_TEXT_TRANSLATION_SERVICE:
		return NewTextTranslateWorker(taskType), nil
	case omniEngine.TaskType_SRT_TRANSLATION_SERVICE:
		return NewSRTTranslateWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS_PRO:
		return NewSubtitlePreprocessProWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATION_ERASE_PRO:
		return NewEraseV2ProWorker(taskType), nil
	case omniEngine.TaskType_AUDIO_LANG_DETECT:
		return NewAudioLangDetectWorker(taskType), nil
	case omniEngine.TaskType_AUDIO_VOLCENGINE_ASR:
		return NewVolcengineAsrWorker(taskType), nil
	case omniEngine.TaskType_AGENT_TRANSLATION_SERVICE:
		return NewAgentTranslateWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSLATE_SUBTITLE_MERGE, omniEngine.TaskType_VIDEO_TRANSLATE_SUBTITLE_MERGE_LONG:
		return NewSubtitleMergeWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_TRANSCODING:
		return NewVideoTranscodingWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_COMMENTARY_QIFEI:
		return NewCommentaryQiFeiWorker(taskType), nil
	case omniEngine.TaskType_COMMENTARY_SUBTITLE_MERGE:
		return NewCommentaryMergeWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_UNDERSTANDING:
		return NewVideoUnderstandingWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_SUBTITLE_GENERATION:
		return NewVideoSubtitleGenerationWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_ALIGNMENT:
		return NewVideoAlignmentWorker(taskType), nil
	case omniEngine.TaskType_VIDEO_HIGHLIGHT_CLIPPING:
		return NewVideoHighlightClippingWorker(taskType), nil
	default:
		return nil, fmt.Errorf("unknown task type: %s", taskType)
	}
}
