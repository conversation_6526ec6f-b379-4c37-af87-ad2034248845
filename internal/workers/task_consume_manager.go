package workers

import (
	"context"
	"sync"

	"omni_worker/internal/client"
	"omni_worker/pkg/config"
)

type taskConsumeManager struct {
	ctx context.Context
	sync.Mutex
	enable         bool
	taskConsumeCtx *taskConsumeCtx
}

type taskConsumeCtx struct {
	currentIndex        int // 使用独立idx
	taskConsumeRuleList []*taskConsumeRule
}

type taskConsumeRule struct {
	Cluster       string
	Count         int
	OriginalCount int // 添加字段保存原始count值
}

// newTaskConsumeManager 创建新的任务调度管理器
func newTaskConsumeManager(ctx context.Context, taskConsumeCfg *config.TaskConsumeConfig) *taskConsumeManager {
	if taskConsumeCfg == nil || !taskConsumeCfg.Enable {
		return &taskConsumeManager{
			enable: false,
		}
	}

	// 深度拷贝TaskConsumeInfo，同时保存原始count值
	taskConsumeRuleList := make([]*taskConsumeRule, 0, len(taskConsumeCfg.TaskConsumeRuleList))
	for _, rule := range taskConsumeCfg.TaskConsumeRuleList {
		// 校验规则的合法性
		if rule.Cluster == "" {
			continue // 跳过空集群名
		}
		// 确保Count为正数
		count := rule.Count
		if count <= 0 {
			continue // 跳过无效的count
		}
		taskConsumeRuleList = append(taskConsumeRuleList, &taskConsumeRule{
			Cluster:       rule.Cluster,
			Count:         count,
			OriginalCount: count,
		})
	}

	// 如果没有有效的配置，返回nil
	if len(taskConsumeRuleList) == 0 {
		return nil
	}

	return &taskConsumeManager{
		ctx: ctx,
		taskConsumeCtx: &taskConsumeCtx{
			currentIndex:        0,
			taskConsumeRuleList: taskConsumeRuleList,
		},
		enable: taskConsumeCfg.Enable,
	}
}

func (m *taskConsumeManager) getConsumeGRPCClient(ctx context.Context) *client.OmniEngineClient {
	return m.getSpecifyGRPCClient(ctx, m.getNextCluster())
}

func (m *taskConsumeManager) getSpecifyGRPCClient(ctx context.Context, cluster string) *client.OmniEngineClient {
	if len(cluster) == 0 {
		cfg := config.GetConfig()
		return client.GetGRPCMgr().GetOmniEngineClient(ctx, cfg.OmniEngineGRPCConfig.DefaultCluster)
	}
	return client.GetGRPCMgr().GetOmniEngineClient(ctx, cluster)
}

// GetNextCluster 获取下一个请求集群
func (m *taskConsumeManager) getNextCluster() string {
	if m == nil || !m.enable {
		return ""
	}

	m.Lock()
	defer m.Unlock()

	consumeCtx := m.taskConsumeCtx
	if consumeCtx == nil || len(consumeCtx.taskConsumeRuleList) == 0 {
		return ""
	}

	ruleList := consumeCtx.taskConsumeRuleList
	ruleListLen := len(ruleList)
	for i := 0; i < ruleListLen; i++ { // 限制循环次数
		// 获取当前规则
		rule := ruleList[consumeCtx.currentIndex]

		// 如果当前规则的count大于0
		if rule.Count > 0 {
			rule.Count--
			Cluster := rule.Cluster

			// 如果当前规则的count用完，移动到下一个索引
			if rule.Count == 0 {
				consumeCtx.currentIndex = (consumeCtx.currentIndex + 1) % ruleListLen
			}

			return Cluster
		}

		// 移动到下一个索引
		consumeCtx.currentIndex = (consumeCtx.currentIndex + 1) % ruleListLen

		// 如果已经遍历了一圈，重置所有count
		if consumeCtx.currentIndex == 0 {
			for _, r := range ruleList {
				r.Count = r.OriginalCount // 使用保存的原始值进行重置
			}
			rule = ruleList[consumeCtx.currentIndex]
			if rule.Count > 0 {
				rule.Count--
				return rule.Cluster
			}
		}
	}
	return ""
}
