package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

// asr校正
type ASRCorrectionReq struct {
	ASRData []*CorrectionData `json:"asr_data"`
	OCRData []*CorrectionData `json:"ocr_data"`
}

type CorrectionData struct {
	Start    float64     `json:"start"`     // 字幕开始时间
	End      float64     `json:"end"`       // 字幕结束时间
	Text     string      `json:"text"`      // 字幕
	AudioUrl string      `json:"audio_url"` // 语音url
	Speaker  string      `json:"speaker"`   // 说话人
	Words    []*WordInfo `json:"words"`     // 字幕分词
	Score    float64     `json:"score"`     // 分数
}

type WordInfo struct {
	Start float64 `json:"start"` // 字幕开始时间
	End   float64 `json:"end"`   // 字幕结束时间
	Word  string  `json:"word"`  // 字幕
}

type ASRCorrectionRes struct {
	Code int                   `json:"code"`
	Msg  string                `json:"message"`
	Data *ASRCorrectionResData `json:"data"`
}

type ASRCorrectionResData struct {
	ASRResult []*CorrectionData `json:"asr_result"`
	Code      int               `json:"error_code"`
}

type ASRCorrectionWorker struct {
	*BaseWorker
	CreateUrl string
	taskType  omniEngine.TaskType
}

func NewASRCorrectionWorker(taskType omniEngine.TaskType) *ASRCorrectionWorker {
	cfg := config.GetConfig()
	return &ASRCorrectionWorker{
		BaseWorker: NewBaseWorker(),
		CreateUrl:  cfg.VideoTranslateService.ASRCorrectionCreateUrl,
		taskType:   taskType,
	}
}

func (w *ASRCorrectionWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *ASRCorrectionWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *ASRCorrectionWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	IdStr := strconv.FormatInt(t.Id, 10)
	ctx = context.WithValue(ctx, consts.ContextKeyEngineTaskId, gctx.StrKey(IdStr))
	req := &ASRCorrectionReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "ASRCorrectionWorker unmarshal failed, err:%s, taskId:%d, content: %s", err, t.Id, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.handleAsrCorrection(ctx, t.Id, req)
}

func (w *ASRCorrectionWorker) handleAsrCorrection(ctx context.Context, taskId int64, req *ASRCorrectionReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 构建查询请求
	resp := &ASRCorrectionRes{}
	err := retry.Do(func() error {
		return w.PostJSON(ctx, w.CreateUrl, req, resp)
	},
		retry.Attempts(3),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "ASRCorrectionWorker handleAsrCorrection submit failed, err:%v, taskId:%d, req:%+v", err, taskId, *req)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("Failed to call query asr correction service: %v", err),
		}
	}

	if resp.Data.Code != 0 {
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("asr correction failed, req:%+v, code:%d", *req, resp.Code),
		}
	}
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    consts.CommonCodeOk,
		Message: resp.Msg,
		Data:    resp,
	}
}
