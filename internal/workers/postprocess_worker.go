package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type MergeSubmitV2Req struct {
	Id           int64  `json:"id"`
	Callback     string `json:"callback"`
	TargetObject string `json:"target_object"`
}

type MergeSubmitV2Res struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

// 合成查询
type MergeQueryV2Req struct {
	Id int64 `json:"id"`
}

type MergeQueryV2Res struct {
	Id        int64  `json:"id"`
	Code      int32  `json:"code"`
	Msg       string `json:"message"`
	SharePath string `json:"share_path"`
}
type PostMergeWorker struct {
	*BaseWorker
	PostMergeCreateUrl string
	PostMergeQueryUrl  string
	taskType           omniEngine.TaskType
}

func NewPostMergeWorker(taskType omniEngine.TaskType) *PostMergeWorker {
	cfg := config.GetConfig()
	return &PostMergeWorker{
		BaseWorker:         NewBaseWorker(),
		taskType:           taskType,
		PostMergeCreateUrl: cfg.VideoTranslateService.MergeV2CreateUrl,
		PostMergeQueryUrl:  cfg.VideoTranslateService.MergeV2QueryUrl,
	}
}

func (w *PostMergeWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *PostMergeWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &MergeSubmitV2Req{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "PostMergeWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.query(ctx, t, req)
}

func (w *PostMergeWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting Merge task: %d voiceId:%v", t.Id, t.VoiceId)
	req := &MergeSubmitV2Req{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "PostMergeWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.handlePostMergeTask(ctx, t, req)
}

func (w *PostMergeWorker) handlePostMergeTask(ctx context.Context, t *omniEngine.Task, req *MergeSubmitV2Req) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handlePostMergeTask] start task, engineId: %v, taskId: %v", t.Id, req.Id)
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[handlePostMergeTask] timeout, engineId:%d, taskId: %v", t.Id, req.Id)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    0,
				Message: "submit task timeout",
			}
		default:
			status, res := w.submitTask(ctx, t, req)
			if status == omniEngine.TaskStatus_NOT_STARTED {
				time.Sleep(time.Second)
			} else {
				return status, res
			}
		}
	}
}

func (w *PostMergeWorker) submitTask(ctx context.Context, t *omniEngine.Task, req *MergeSubmitV2Req) (omniEngine.TaskStatus, *TaskOutputContent) {
	res := &MergeSubmitV2Res{}
	err := retry.Do(func() error {
		return w.PostJSON(ctx, w.PostMergeCreateUrl, req, res)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "[submitTask] HTTP req failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("http req failed:%s", err),
		}
	}
	switch res.Code {
	case consts.AiCodeOk:
		g.Log().Infof(ctx, "[submitTask] submit ok, engineId: %v, taskId: %v", t.Id, req.Id)
		return omniEngine.TaskStatus_IN_PROGRESS, &TaskOutputContent{
			Code:    consts.CommonCodeOk,
			Message: "ok",
			Data:    res,
		}
	case consts.AiCodeBusy:
		// 预处理繁忙，继续尝试提交
		g.Log().Infof(ctx, "[submitTask] post merge busy, retry commit, msg:%s, id: %v, engineId: %v", res.Msg, req.Id, t.Id)
		return omniEngine.TaskStatus_NOT_STARTED, nil
	case consts.AiCodeParamsErr:
		g.Log().Errorf(ctx, "[submitTask] task failed, err:%s, content: %+v", res.Msg, req)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("task failed:%s", res.Msg),
			Data:    res,
		}
	default:
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("unknown code:%d", res.Code),
			Data:    res,
		}
	}
}

func (w *PostMergeWorker) query(ctx context.Context, t *omniEngine.Task, req *MergeSubmitV2Req) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 构建查询请求
	queryReq := &MergeQueryV2Req{Id: req.Id}
	resp := &MergeQueryV2Res{}
	beginTime := time.Now()
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	retryCount := 0
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[query] timeout, engineId: %v, taskId: %v, beginTime: %v",
				t.Id, req.Id, beginTime.Format(time.DateTime))
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "query result timeout",
			}
		default:
			err := retry.Do(func() error {
				return w.PostJSON(ctx, w.PostMergeQueryUrl, queryReq, resp)
			},
				retry.Attempts(5),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "[query] http request failed: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("http request failed:%s", err),
				}
			}
			switch resp.Code {
			case consts.AiCodeOk:
				g.Log().Infof(ctx, "[query] task completed, engineId: %v, taskId: %v, timeCost: %v",
					t.Id, req.Id, time.Since(beginTime).Seconds())
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    consts.CommonCodeOk,
					Message: "ok",
					Data:    resp,
				}
			case consts.AiCodeBusy:
				g.Log().Infof(ctx, "[query] post merge in progress, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				time.Sleep(time.Second)
			case consts.AiCodeTaskNotExist, consts.AiCodeCudaOutOfMemory:
				g.Log().Infof(ctx, "[query] retry submit task, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				retryCount++
				if retryCount > 3 {
					return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
						Code:    consts.CommonCodeFail,
						Message: "attempt all retry failed",
					}
				}
				// 任务不存在或者内存不足，需要重新提交
				status, res := w.handlePostMergeTask(ctx, t, req)
				// 重新提交成功，继续查询结果
				if status == omniEngine.TaskStatus_IN_PROGRESS {
					continue
				}
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: "retry submit task failed",
					Data:    res,
				}
			case consts.AiCodePullVideoFailed, consts.AiCodeParamsErr,
				consts.AiCodeInternalError:
				g.Log().Infof(ctx, "[query] task failed, taskId: %v, engineId: %v, code: %v, msg: %s",
					req.Id, t.Id, resp.Code, resp.Msg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: resp.Msg,
					Data:    resp,
				}
			default:
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("unknown code:%d", resp.Code),
					Data:    resp,
				}
			}
		}
	}
}
