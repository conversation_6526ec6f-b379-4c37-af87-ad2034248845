package workers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"omni_worker/internal/client"
	"omni_worker/internal/consts"
	"omni_worker/internal/monitor"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	jsoniter "github.com/json-iterator/go"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

var taskTypeEnum2TaskType = map[consts.TaskTypeEnum]omniEngine.TaskType{
	consts.TaskTypeEnumTTSMultiLang:                    omniEngine.TaskType_TTS_SERVICE_MULTILANG,
	consts.TaskTypeEnumTTSPvc:                          omniEngine.TaskType_TTS_SERVICE_PVC,
	consts.TaskTypeEnumMinimaxTTS:                      omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_MINIMAX,
	consts.TaskTypeEnum11LabsTTS:                       omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_11LABS,
	consts.TaskTypeEnumAudioSeparate:                   omniEngine.TaskType_AUDIO_SEPARATE,
	consts.TaskTypeEnumASR:                             omniEngine.TaskType_ASR_SERVICE,
	consts.TaskTypeEnumSTS:                             omniEngine.TaskType_STS_SERVICE,
	consts.TaskTypeEnumVoiceIsolate:                    omniEngine.TaskType_VOICE_ISOLATE_SERVICE,
	consts.TaskTypeEnumVideoTranslateErase:             omniEngine.TaskType_VIDEO_TRANSLATION_ERASE,
	consts.TaskTypeEnumVideoTranslateExtract:           omniEngine.TaskType_VIDEO_TRANSLATION_OCR_EXTRACT,
	consts.TaskTypeEnumVideoTranslatePreprocess:        omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS,
	consts.TaskTypeEnumVideoTranslatePostMerge:         omniEngine.TaskType_VIDEO_TRANSLATION_POST_MERGE,
	consts.TaskTypeEnumVideoTranslateV2AsrCorrect:      omniEngine.TaskType_VIDEO_TRANSLATION_V2_ASR_CORRECT,
	consts.TaskTypeEnumVideoTranslateV2TtsCorrect:      omniEngine.TaskType_VIDEO_TRANSLATION_V2_TTS_CORRECT,
	consts.TaskTypeEnumTextTranslate:                   omniEngine.TaskType_TEXT_TRANSLATION_SERVICE,
	consts.TaskTypeEnumSrtTextTranslate:                omniEngine.TaskType_SRT_TRANSLATION_SERVICE,
	consts.TaskTypeEnumAgentTranslate:                  omniEngine.TaskType_AGENT_TRANSLATION_SERVICE,
	consts.TaskTypeEnumAudioVolcengineAsr:              omniEngine.TaskType_AUDIO_VOLCENGINE_ASR,
	consts.TaskTypeEnumAudioLangDetect:                 omniEngine.TaskType_AUDIO_LANG_DETECT,
	consts.TaskTypeEnumVideoTranslatePreprocessPro:     omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS_PRO,
	consts.TaskTypeEnumVideoTranslateErasePro:          omniEngine.TaskType_VIDEO_TRANSLATION_ERASE_PRO,
	consts.TaskTypeEnumVideoTranslateSubtitleMerge:     omniEngine.TaskType_VIDEO_TRANSLATE_SUBTITLE_MERGE,
	consts.TaskTypeEnumVideoTranslateSubtitleMergeLong: omniEngine.TaskType_VIDEO_TRANSLATE_SUBTITLE_MERGE_LONG,
	consts.TaskTypeEnumVideoTranscoding:                omniEngine.TaskType_VIDEO_TRANSCODING,
	consts.TaskTypeEnumVideoCommentaryQiFei:            omniEngine.TaskType_VIDEO_COMMENTARY_QIFEI,
	consts.TaskTypeEnumCommentarySubtitleMerge:         omniEngine.TaskType_COMMENTARY_SUBTITLE_MERGE,
	consts.TaskTypeEnumVideoUnderstanding:              omniEngine.TaskType_VIDEO_UNDERSTANDING,
	consts.TaskTypeEnumVideoSubtitleGeneration:         omniEngine.TaskType_VIDEO_SUBTITLE_GENERATION,
	consts.TaskTypeEnumVideoAlignment:                  omniEngine.TaskType_VIDEO_ALIGNMENT,
	consts.TaskTypeEnumVideoHighlightClipping:          omniEngine.TaskType_VIDEO_HIGHLIGHT_CLIPPING,
}

var (
	taskHandlerMgr        *taskHandlerManager
	healthCheckStatus     bool
	healthCheckStatusMu   sync.RWMutex
	reportAlgoVersionOnce sync.Once
)

func getHealthCheckStatus() bool {
	healthCheckStatusMu.RLock()
	defer healthCheckStatusMu.RUnlock()
	return healthCheckStatus
}

func setHealthCheckStatus(status bool) {
	healthCheckStatusMu.Lock()
	defer healthCheckStatusMu.Unlock()
	healthCheckStatus = status
}

type taskHandlerManager struct {
	taskHandlerList      []taskHandler
	omniEngineGRPCConfig *config.OmniEngineGRPCConfig
}

func (w *taskHandlerManager) refreshAllHandlerTaskConsumeConfig(ctx context.Context) {
	cfg := config.GetConfig()
	if w.omniEngineGRPCConfig.DeepEqual(cfg.OmniEngineGRPCConfig) {
		g.Log().Infof(ctx, "task consume config not changed")
		return
	}
	beforeCfgStr, _ := jsoniter.MarshalToString(w.omniEngineGRPCConfig)
	afterCfgStr, _ := jsoniter.MarshalToString(cfg.OmniEngineGRPCConfig)
	g.Log().Infof(ctx, "refresh task consume config, before:%s, after:%s", beforeCfgStr, afterCfgStr)
	w.omniEngineGRPCConfig = cfg.OmniEngineGRPCConfig.DeepCopy()
	// 重建grpc连接
	client.InitGRPCClient(ctx, cfg)
	for _, handler := range w.taskHandlerList {
		handler.refreshTaskConsumeConfig(ctx, newTaskConsumeManager(ctx, cfg.OmniEngineGRPCConfig.TaskConsumeConfig))
	}
}

func Run(ctx context.Context, taskTypeEnumList []string, healthCheckSw string) {
	taskHandlerMgr = &taskHandlerManager{
		omniEngineGRPCConfig: config.GetConfig().OmniEngineGRPCConfig.DeepCopy(),
	}
	cfg := config.GetConfig()
	// 注册配置变更回调
	config.RegisterConfigChangeCallback(func() {
		taskHandlerMgr.refreshAllHandlerTaskConsumeConfig(ctx)
	})
	workerFactory := NewWorkerFactory()
	for _, taskTypeEnum := range taskTypeEnumList {
		taskType, ok := taskTypeEnum2TaskType[consts.TaskTypeEnum(taskTypeEnum)]
		if !ok {
			g.Log().Errorf(ctx, "taskTypeEnum not found, taskTypeEnum:%s", taskTypeEnum)
			panic(errors.New("taskTypeEnum not found"))
		}
		worker, err := workerFactory.CreateWorker(taskType)
		if err != nil {
			g.Log().Errorf(ctx, "create worker failed, taskType:%s, err:%s", taskType, err)
			panic(err)
		}
		if worker == nil {
			g.Log().Errorf(ctx, "worker is nil, taskType:%s", taskTypeEnum)
			panic(errors.New("worker is nil"))
		}
		healthUrl := ""
		if healthCheckSw != "disable" {
			healthUrl = config.GetHealthCheckURL(taskType)
		}
		restClient := trace.NewXHttpClient()
		taskConsumeManager := newTaskConsumeManager(ctx, cfg.OmniEngineGRPCConfig.TaskConsumeConfig)
		handler := NewTaskHandler(&baseTaskHandler{
			taskType:           taskType,
			executingTaskIDs:   sync.Map{},
			workerCfg:          cfg.GetWorkerCfg(consts.TaskTypeEnum(taskTypeEnum)),
			healthUrl:          healthUrl,
			restClient:         restClient,
			healthCheckSw:      healthCheckSw,
			taskConsumeManager: taskConsumeManager,
		}, worker)
		taskHandlerMgr.taskHandlerList = append(taskHandlerMgr.taskHandlerList, handler)
		handler.start(ctx)
		g.Log().Infof(ctx, "%s worker started", taskTypeEnum)
	}
}

func NewTaskHandler(baseHandler *baseTaskHandler, worker Worker) taskHandler {
	switch baseHandler.taskType {
	case omniEngine.TaskType_VIDEO_TRANSLATION_ERASE, omniEngine.TaskType_VIDEO_TRANSLATION_OCR_EXTRACT,
		omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS, omniEngine.TaskType_VIDEO_TRANSLATION_POST_MERGE,
		omniEngine.TaskType_VIDEO_TRANSLATION_ERASE_PRO, omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS_PRO,
		omniEngine.TaskType_TTS_SERVICE_PVC, omniEngine.TaskType_TTS_SERVICE_MULTILANG:
		return NewVariableConcurrencyTaskHandler(baseHandler, worker)
	default:
		return NewFixConcurrencyTaskHandler(baseHandler, worker)
	}
}

type taskHandler interface {
	start(ctx context.Context)
	refreshTaskConsumeConfig(ctx context.Context, newMgr *taskConsumeManager)
}

// 封装基础能力的handler
type baseTaskHandler struct {
	taskType         omniEngine.TaskType
	executingTaskIDs sync.Map
	workerCfg        *config.WorkerConfig
	healthUrl        string
	restClient       *trace.XHttpClient
	healthCheckSw    string
	sync.RWMutex
	*taskConsumeManager
}

// 上报执行中任务信息
type reportTask struct {
	taskId  int64
	cluster string
}

func (w *baseTaskHandler) getTaskConsumeManager() *taskConsumeManager {
	w.RLock()
	defer w.RUnlock()
	return w.taskConsumeManager
}

func (w *baseTaskHandler) refreshTaskConsumeConfig(ctx context.Context, newMgr *taskConsumeManager) {
	w.Lock()
	defer w.Unlock()
	w.taskConsumeManager = newMgr
}

func (w *baseTaskHandler) healthCheck(ctx context.Context) {
	if w.healthCheckSw == "disable" {
		g.Log().Infof(ctx, "healthCheck: healthCheckSw is disable, taskType = %d", w.taskType)
		setHealthCheckStatus(true)
		return
	}
	// 持续健康检查
	for {
		g.Log().Infof(ctx, "healthCheck: taskType = %d, start", w.taskType)
		healthCheckStatus = w.getHealthStatus(ctx)
		if !healthCheckStatus {
			setHealthCheckStatus(healthCheckStatus)
		} else if !getHealthCheckStatus() {
			// 健康检查状态为false才设置为true，减少锁冲突。
			setHealthCheckStatus(healthCheckStatus)
		}
		g.Log().Infof(ctx, "healthCheck: taskType = %d, end, status:%v", w.taskType, getHealthCheckStatus())

		time.Sleep(time.Duration(10) * time.Second)
	}
}

func (w *baseTaskHandler) getHealthStatus(ctx context.Context) bool {
	response := &struct {
		Code    int    `json:"code"`
		Version string `json:"version"`
	}{}
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Second*15)
	defer cancel()
	resp, err := w.restClient.R(timeoutCtx).
		SetContext(timeoutCtx).
		SetHeader("Content-Type", "application/json").
		SetResult(response). // 设置响应结构体
		Get(w.healthUrl)
	if err != nil {
		g.Log().Errorf(ctx, "healthCheck: HTTP request failed: %v", err)
		return false
	}
	if resp.StatusCode() != 200 {
		g.Log().Warningf(ctx, "healthCheck not completed, taskType:%d, StatusCode = %d", w.taskType, resp.StatusCode())
		return false
	}
	reportAlgoVersionOnce.Do(func() {
		if response != nil && response.Version != "" {
			w.getTaskConsumeManager().getSpecifyGRPCClient(ctx, "").ReportAlgorithmVersion(ctx, w.taskType, response.Version)
		}
	})
	return true
}

func (w *baseTaskHandler) genTaskCtx(ctx context.Context, task *omniEngine.Task) context.Context {
	if task == nil {
		return ctx
	}
	if task.Extra != nil {
		ctx = context.WithValue(ctx, trace.ReqId, task.Extra[trace.ReqId])
		ctx = context.WithValue(ctx, trace.TrafficMark, task.Extra[trace.TrafficMark])
		ctx = context.WithValue(ctx, consts.TaskContextKeyCluster, task.Extra[consts.TaskContextKeyCluster])
	}
	ctx = context.WithValue(ctx, consts.TaskId, task.Id)
	ctx = context.WithValue(ctx, trace.TenantId, task.TenantId)
	return ctx
}

type updateTaskBO struct {
	Task          *omniEngine.Task
	TaskStatus    omniEngine.TaskStatus
	TaskOutput    interface{}
	WorkerStartAt time.Time
	WorkerEndAt   time.Time
}

func (w *baseTaskHandler) updateTask(ctx context.Context, bo *updateTaskBO) error {
	resultJSON, err := json.Marshal(bo.TaskOutput)
	if err != nil {
		return err
	}
	var cluster string
	if bo.Task.Extra != nil {
		cluster = bo.Task.Extra[consts.TaskContextKeyCluster]
	}
	g.Log().Infof(ctx, "update task status, cluster: %s, taskId: %d, taskType: %d, taskResult: %s", cluster, bo.Task.Id, bo.Task.Type, string(resultJSON))
	return retry.Do(func() error {
		return w.getTaskConsumeManager().getSpecifyGRPCClient(ctx, cluster).UpdateTask(ctx, bo.Task.Id, bo.TaskStatus, string(resultJSON), bo.WorkerStartAt, bo.WorkerEndAt)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
}

func (w *baseTaskHandler) fetchTask(ctx context.Context) (*omniEngine.Task, context.Context, error) {
	if !getHealthCheckStatus() {
		g.Log().Warningf(ctx, "fetchTask: healthCheckStatus is false, taskType = %d", w.taskType)
		return nil, nil, fmt.Errorf("healthCheck: healthCheckStatus is false, taskType = %d", w.taskType)
	}
	task, err := w.getTaskConsumeManager().getConsumeGRPCClient(ctx).GetTask(ctx, w.taskType)
	if err != nil {
		return nil, nil, err
	}
	if task == nil {
		return nil, nil, fmt.Errorf("no %s tasks available in queue", w.taskType)
	}
	// 设置日志关键信息
	taskCtx := w.genTaskCtx(ctx, task)
	// 仅对特定任务类型打印详细日志，其他任务打印简要日志
	switch task.Type {
	case omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS, omniEngine.TaskType_VIDEO_TRANSLATION_PREPROCESS_PRO:
		g.Log().Infof(taskCtx, "fetch task: tenantId: %v, engineId: %v, type: %v", task.TenantId, task.Id, task.Type)
	default:
		g.Log().Infof(taskCtx, "fetch task: %+v", task)
	}
	return task, taskCtx, nil
}

func (w *baseTaskHandler) processTaskPanic(ctx context.Context, bo *updateTaskBO) error {
	g.Log().Errorf(ctx, "processTaskPanic, taskID:%d, status change to fail", bo.Task.Id)
	bo.TaskStatus = omniEngine.TaskStatus_FAILED
	bo.TaskOutput = &TaskOutputContent{
		Code:    CodeTaskFailed,
		Message: "task panic",
	}
	bo.WorkerEndAt = time.Now()
	return w.updateTask(ctx, bo)
}

func (w *baseTaskHandler) addExecutingTaskID(task *omniEngine.Task) {
	var cluster string
	if task.Extra != nil {
		cluster = task.Extra[consts.TaskContextKeyCluster]
	}
	w.executingTaskIDs.Store(task.Id, reportTask{
		taskId:  task.Id,
		cluster: cluster,
	})
}

func (w *baseTaskHandler) removeExecutingTaskID(taskID int64) {
	w.executingTaskIDs.Delete(taskID)
}

func (w *baseTaskHandler) reportRunningTasks(ctx context.Context) {
	ticker := time.NewTicker(time.Duration(w.workerCfg.ReportSeconds) * time.Second)
	for range ticker.C {
		cluster2TaskList := make(map[string][]*omniEngine.TaskStatusReport)
		w.executingTaskIDs.Range(func(key, value interface{}) bool {
			reportTask, ok := value.(reportTask)
			if ok {
				if cluster2TaskList[reportTask.cluster] == nil {
					cluster2TaskList[reportTask.cluster] = make([]*omniEngine.TaskStatusReport, 0)
				}
				cluster2TaskList[reportTask.cluster] = append(cluster2TaskList[reportTask.cluster], &omniEngine.TaskStatusReport{
					TaskId: reportTask.taskId,
				})
			}
			return true
		})
		for cluster, taskList := range cluster2TaskList {
			g.Log().Infof(ctx, "reportRunningTasks, cluster:%s, taskList:%+v", cluster, taskList)
			err := retry.Do(func() error {
				return w.getTaskConsumeManager().getSpecifyGRPCClient(ctx, cluster).ReportTasksStatus(ctx, taskList)
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "reportRunningTasks failed, err:%s", err)
			}
		}
	}
}

// 适用于算法服务固定并发数的任务
type FixConcurrencyTaskHandler struct {
	Worker
	*baseTaskHandler
}

func NewFixConcurrencyTaskHandler(baseHandler *baseTaskHandler, worker Worker) *FixConcurrencyTaskHandler {
	return &FixConcurrencyTaskHandler{
		Worker:          worker,
		baseTaskHandler: baseHandler,
	}
}

func (w *FixConcurrencyTaskHandler) run(ctx context.Context) {
	ticker := time.NewTicker(time.Duration(w.workerCfg.IntervalMilliseconds) * time.Millisecond)
	for range ticker.C {
		select {
		// 处理一下优雅停机
		case <-ctx.Done():
			monitor.DecWorkerRunCount(w.taskType)
			return
		default:
			_ = w.do()
		}
	}
}

func (w *FixConcurrencyTaskHandler) do() error {
	// 1. 获取任务
	task, taskCtx, err := w.fetchTask(gctx.New())
	if err != nil {
		return err
	}
	startTime := time.Now()
	defer w.removeExecutingTaskID(task.Id)
	defer func() {
		monitor.EndTask(w.taskType, task.BizType, task.Id)
	}()
	defer func() {
		if err := recover(); err != nil {
			g.Log().Errorf(taskCtx, "panic: %v", err)
			updateErr := w.processTaskPanic(taskCtx, &updateTaskBO{
				Task:          task,
				WorkerStartAt: startTime,
			})
			if updateErr != nil {
				g.Log().Errorf(taskCtx, "updateTaskStatus failed, err:%s", updateErr)
			}
		}
	}()
	monitor.StartTask(w.taskType, task.BizType, task.Id)
	// 2. 记录正在执行的任务ID
	w.addExecutingTaskID(task)
	// 3. 执行任务
	status, output := w.Process(taskCtx, task)
	quantity, ok := task.GetExtra()["task_cost"]
	if !ok {
		quantity = "0"
	}
	costTime := time.Since(startTime).Seconds()
	monitor.ObserveTaskExecuteCostSeconds(w.taskType, task.BizType, costTime, quantity, status)

	// 4. 更新任务状态
	err = w.updateTask(taskCtx, &updateTaskBO{
		Task:          task,
		TaskStatus:    status,
		TaskOutput:    output,
		WorkerStartAt: startTime,
		WorkerEndAt:   time.Now(),
	})
	if err != nil {
		g.Log().Errorf(taskCtx, "updateTaskStatus failed, err:%s", err)
		return err
	}
	return nil
}

func (w *FixConcurrencyTaskHandler) start(ctx context.Context) {
	concurrent.GoSafe(func() {
		w.reportRunningTasks(ctx)
	})
	concurrent.GoSafe(func() {
		w.healthCheck(ctx)
	})
	for i := 0; i < w.workerCfg.MaxConcurrentTasks; i++ {
		g.Log().Infof(ctx, "start worker[%d]:%s", i, w.taskType)
		concurrent.GoSafe(func() {
			w.run(ctx)
		})
	}
	monitor.SetWorkerRunCount(w.taskType, float64(w.workerCfg.MaxConcurrentTasks))
}

// 适用于算法服务并发数未知的任务
type VariableConcurrencyTaskHandler struct {
	Worker
	*baseTaskHandler
}

func NewVariableConcurrencyTaskHandler(baseHandler *baseTaskHandler, worker Worker) *VariableConcurrencyTaskHandler {
	return &VariableConcurrencyTaskHandler{
		Worker:          worker,
		baseTaskHandler: baseHandler,
	}
}

type callbackData struct {
	Ctx       context.Context
	Task      *omniEngine.Task
	ExtraData any
	startTime time.Time
}

func (w *VariableConcurrencyTaskHandler) start(ctx context.Context) {
	concurrent.GoSafe(func() {
		w.reportRunningTasks(ctx)
	})
	processDoneCh := make(chan callbackData, 1)
	concurrent.GoSafe(func() {
		w.healthCheck(ctx)
	})
	concurrent.GoSafe(func() {
		w.run(ctx, processDoneCh)
	})
	concurrent.GoSafe(func() {
		w.after(ctx, processDoneCh)
	})
	monitor.SetWorkerRunCount(w.taskType, float64(w.workerCfg.MaxConcurrentTasks))
}

func (w *VariableConcurrencyTaskHandler) run(ctx context.Context, processDoneCh chan callbackData) {
	ticker := time.NewTicker(time.Duration(w.workerCfg.IntervalMilliseconds) * time.Millisecond)
	for range ticker.C {
		select {
		case <-ctx.Done():
			// 处理一下优雅停机
			monitor.DecWorkerRunCount(w.taskType)
			return
		default:
			w.handleProcess(processDoneCh)
		}
	}
}

func (w *VariableConcurrencyTaskHandler) handleProcess(processDoneCh chan callbackData) {
	// 1. 获取任务
	task, taskCtx, err := w.fetchTask(gctx.New())
	if err != nil {
		return
	}
	startTime := time.Now()
	defer func() {
		if err := recover(); err != nil {
			g.Log().Errorf(taskCtx, "panic: %v", err)
			w.removeExecutingTaskID(task.Id)
			updateErr := w.processTaskPanic(taskCtx, &updateTaskBO{
				Task:          task,
				WorkerStartAt: startTime,
			})
			if updateErr != nil {
				g.Log().Errorf(taskCtx, "updateTaskStatus failed, err:%s", updateErr)
			}
		}
	}()
	g.Log().Infof(taskCtx, "handleProcess, taskId:%v", task.Id)
	// 2. 记录正在执行的任务ID
	w.addExecutingTaskID(task)
	// 3. 执行任务
	g.Log().Infof(taskCtx, "process task start, taskId:%v", task.Id)
	status, output := w.Process(taskCtx, task)
	g.Log().Infof(taskCtx, "process task done, taskId:%v, status:%d, output:%+v", task.Id, status, output)

	// 任务进行中则开始轮询结果
	switch status {
	case omniEngine.TaskStatus_IN_PROGRESS:
		// 提交成功才上报执行中任务数量
		monitor.StartTask(w.taskType, task.BizType, task.Id)
		processDoneCh <- callbackData{
			Ctx:       taskCtx,
			Task:      task,
			ExtraData: output,
			startTime: time.Now(), // 提交成功则记录开始时间
		}
	default:
		err = w.updateTask(taskCtx, &updateTaskBO{
			Task:          task,
			TaskStatus:    status,
			TaskOutput:    output,
			WorkerStartAt: startTime,
			WorkerEndAt:   time.Now(),
		})
		if err != nil {
			g.Log().Errorf(taskCtx, "updateTaskStatus failed, err:%s", err)
		}
		w.removeExecutingTaskID(task.Id)
	}
}

func (w *VariableConcurrencyTaskHandler) after(ctx context.Context, processDoneCh chan callbackData) {
	for {
		select {
		case <-ctx.Done():
			return
		case cbData, ok := <-processDoneCh:
			if !ok {
				return
			}
			concurrent.GoSafe(func() {
				w.handleCallback(cbData)
			})
		}
	}
}

func (w *VariableConcurrencyTaskHandler) handleCallback(cbData callbackData) {
	ctx := cbData.Ctx
	status := omniEngine.TaskStatus_FAILED
	output := &TaskOutputContent{}
	defer w.removeExecutingTaskID(cbData.Task.Id)
	defer monitor.EndTask(w.taskType, cbData.Task.BizType, cbData.Task.Id)
	defer func() {
		if err := recover(); err != nil {
			g.Log().Errorf(ctx, "panic: %v", err)
			updateErr := w.processTaskPanic(ctx, &updateTaskBO{
				Task:          cbData.Task,
				WorkerStartAt: cbData.startTime,
			})
			if updateErr != nil {
				g.Log().Errorf(ctx, "updateTaskStatus failed, err:%s", updateErr)
			}
		}
	}()

	// 执行回调
	status, output = w.Callback(ctx, cbData.Task, cbData.ExtraData)
	quantity, ok := cbData.Task.GetExtra()["task_cost"]
	if !ok {
		quantity = "0"
	}
	costTime := w.getCostTime(ctx, cbData, output)
	monitor.ObserveTaskExecuteCostSeconds(w.taskType, cbData.Task.BizType, costTime, quantity, status)
	g.Log().Infof(ctx, "handleCallback, taskId:%d, status:%d, output:%+v", cbData.Task.Id, status, output)
	err := w.updateTask(ctx, &updateTaskBO{
		Task:          cbData.Task,
		TaskStatus:    status,
		TaskOutput:    output,
		WorkerStartAt: cbData.startTime,
		WorkerEndAt:   time.Now(),
	})
	if err != nil {
		g.Log().Errorf(ctx, "updateTaskStatus failed, err:%s", err)
	}
}

func (w *VariableConcurrencyTaskHandler) getCostTime(ctx context.Context, cbData callbackData, output *TaskOutputContent) float64 {
	costTime := time.Since(cbData.startTime).Seconds()
	switch w.taskType {
	case omniEngine.TaskType_TTS_SERVICE_PVC:
		if ttsData, ok := output.Data.(*TTSResponse); ok {
			if runTime, ok := ttsData.ExtraData["run_time"]; ok {
				costTime = runTime.(float64)
				g.Log().Debugf(ctx, "run_time is found, taskId:%d, run_time:%f", cbData.Task.Id, costTime)
			} else {
				g.Log().Errorf(ctx, "run_time is not found, taskId:%d", cbData.Task.Id)
			}
		} else {
			g.Log().Errorf(ctx, "ttsData is not *TTSData, taskId:%d", cbData.Task.Id)
		}
	}
	return costTime
}
