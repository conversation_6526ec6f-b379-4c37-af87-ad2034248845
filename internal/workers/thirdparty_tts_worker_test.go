package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"omni_worker/pkg/config"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

func TestThirdPartyTTSWorker_callMinimaxTTSService(t *testing.T) {
	tests := []struct {
		name           string
		httpCode       int
		responseBody   string
		expectedError  string
		expectedResult *MinimaxTTSResponse
	}{
		{
			name:     "success_case",
			httpCode: http.StatusOK,
			responseBody: `{
				"code": 0,
				"request_id": "test-req-123",
				"url": "https://example.com/audio.mp3",
				"duration_ms": 1000
			}`,
			expectedError: "",
			expectedResult: &MinimaxTTSResponse{
				Code:       0,
				RequestID:  "test-req-123",
				URL:        "https://example.com/audio.mp3",
				DurationMs: 1000,
			},
		},
		{
			name:     "unauthorized_error",
			httpCode: http.StatusUnauthorized,
			responseBody: `{
				"error": {
					"code": 401,
					"message": "Invalid authorization token"
				}
			}`,
			expectedError:  "Minimax TTS service error: Invalid authorization token (code=401)",
			expectedResult: nil,
		},
		{
			name:     "rate_limit_error",
			httpCode: http.StatusTooManyRequests,
			responseBody: `{
				"error": {
					"code": 102004,
					"message": "请求太频繁，请稍后再试"
				}
			}`,
			expectedError:  "",
			expectedResult: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试服务器
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// 验证请求头
				assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
				assert.Equal(t, "damoxingbeianshenhe142:d403963a84c2c5d2b4bd4879891c0521", r.Header.Get("Authorization"))

				// 验证请求体
				var reqBody MinimaxTTSRequest
				err := json.NewDecoder(r.Body).Decode(&reqBody)
				assert.NoError(t, err)

				expectedRequest := MinimaxTTSRequest{
					Model:          "speech-02-",
					Input:          "你好, 请说一句话",
					Voice:          "male-qn-badao",
					Speed:          1.0,
					ResponseFormat: "mp3",
					ModelExtra: map[string]interface{}{
						"audio_sample_rate": float64(16000),
						"emotion":           "happy",
					},
				}

				assert.Equal(t, expectedRequest.Model, reqBody.Model)
				assert.Equal(t, expectedRequest.Input, reqBody.Input)
				assert.Equal(t, expectedRequest.Voice, reqBody.Voice)
				assert.Equal(t, expectedRequest.Speed, reqBody.Speed)
				assert.Equal(t, expectedRequest.ResponseFormat, reqBody.ResponseFormat)
				assert.Equal(t, expectedRequest.ModelExtra, reqBody.ModelExtra)

				// 返回测试响应
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(tt.httpCode)
				w.Write([]byte(tt.responseBody))
			}))
			defer server.Close()

			// 创建测试 worker
			worker := &ThirdPartyTTSWorker{
				BaseWorker:        NewBaseWorker(),
				taskType:          omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_MINIMAX,
				thirdPartyTTSUrl:  "https://aigc-backend.skyengine.com.cn/eliza/audio/speech",
				thirdPartyAuthKey: "damoxingbeianshenhe142:d403963a84c2c5d2b4bd4879891c0521",
				thirdPartyModel:   "speech-02-hd",
				retryConfig: config.RetryConfig{
					RetryInterval: 5,
					MaxRetryCount: 2,
					BaseDelay:     1,
					MaxDelay:      30,
				},
			}

			// 构建请求
			request := MinimaxTTSRequest{
				Model:          "speech-02-hd-preview",
				Input:          "你好, 请说一句话",
				Voice:          "male-qn-badao",
				Speed:          1.0,
				ResponseFormat: "mp3",
				ModelExtra: map[string]interface{}{
					"audio_sample_rate": 16000,
					"emotion":           "happy",
				},
			}
			for i := 0; i < 500; i++ {
				go func() {
					// 执行测试
					result, err := worker.callMinimaxTTSService(context.Background(), request)

					// 验证结果
					if tt.expectedError != "" {
						assert.Error(t, err)
						assert.Contains(t, err.Error(), tt.expectedError)
						assert.Nil(t, result)
					} else {
						assert.NoError(t, err)
						assert.NotNil(t, result)
						assert.Equal(t, tt.expectedResult.Code, result.Code)
						assert.Equal(t, tt.expectedResult.RequestID, result.RequestID)
						assert.Equal(t, tt.expectedResult.URL, result.URL)
						assert.Equal(t, tt.expectedResult.DurationMs, result.DurationMs)
					}
				}()
			}
			// 等待所有 goroutine 完成
			time.Sleep(30 * time.Second)
		})
	}
}

func TestThirdPartyTTSWorker_handleElevenLabsTTSTask(t *testing.T) {
	PatchConvey("TestThirdPartyTTSWorker_handleElevenLabsTTSTask", t, func() {
		Mock(config.GetConfig).Return(&config.Config{
			ElevenLabsConfig: config.ElevenLabsConfig{
				// TTSModelId:     "eleven_turbo_v2_5",
				ApiKey:         "***************************************************",
				CreateTTSUrl:   "https://api.elevenlabs.io/v1/text-to-speech/%s",
				CreateVoiceUrl: "https://api.elevenlabs.io/v1/voices/add",
				DeleteVoiceUrl: "https://api.elevenlabs.io/v1/voices/%s",
				DefaultModelId: "eleven_multilingual_v2",
				ModelIdMap:     map[string]string{},
			},
			OBS: config.OBSConfig{
				Vendor:    "ali",
				AccessKey: "LTAI5tKgvBShWP92WXvDoHpt",
				SecretKey: "******************************",
				Bucket:    "oss-test-ali-bj-xp-allvoice-cn",
				Endpoint:  "oss-cn-beijing.aliyuncs.com",
				CDN:       "cdn-allvoice-down-cn-testing.funnycp.com",
				CosUrl:    "",
				ObjectDir: "openapi",
			},
		}).Build()
		worker := &ThirdPartyTTSWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_11LABS,
		}
		status, output := worker.handleElevenLabsTTSTask(context.Background(), ThirdPartyTTSRequest{
			SourceUrl:  "https://cdn-allvoice-down-cn-testing.funnycp.com/translate-saas-test/public/280384786571198576/2025-09-10/301261730971975746/2396a0b1-94f0-461d-92cb-edbf68200aba.mp3",
			TargetUrl:  "allvoice/translate-saas-test/public/videotranslate/280384786571198576/2025-09-11/301261730971975746/c7f5-45c0-b245-97bfc71508f2_generate_audio.mp3",
			SourceText: "医生，我还有多久啊？",
			TargetText: "Dottore, quanto tempo mi resta?",
			SourceLang: "zh",
			TargetLang: "ru",
		})
		So(status, ShouldEqual, omniEngine.TaskStatus_COMPLETED)
		So(output, ShouldNotBeNil)
	})
}

func TestDeleteElevenLabsTTSTask(t *testing.T) {
	PatchConvey("TestThirdPartyTTSWorker_handleElevenLabsTTSTask", t, func() {
		Mock(config.GetConfig).Return(&config.Config{
			ElevenLabsConfig: config.ElevenLabsConfig{
				// TTSModelId:     "eleven_turbo_v2_5",
				ApiKey:         "***************************************************",
				CreateTTSUrl:   "https://api.elevenlabs.io/v1/text-to-speech/%s",
				CreateVoiceUrl: "https://api.elevenlabs.io/v1/voices/add",
				DeleteVoiceUrl: "https://api.elevenlabs.io/v1/voices/%s",
			},
		}).Build()
		worker := &ThirdPartyTTSWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_TTS_SERVICE_THIRDPARTY_11LABS,
		}
		voiceIds := []string{"T69w5F7p9O7bwwVOBPCT", "3ooAVsbI1exDYmYdFtm5", "9epU4uzYwVaOIjB9ZYVN", "Cw8Abqxbm3iqmagoXM6e", "Q0HkLxwkqnDJcvHzDgXn", "2n9CGDuNPOr6UIz9SrH9", "Mc7WaLsa8FiCvbqmCNu1", "hNJ7GC85Ceo4IL3nogN1", "dEeLeFvdltaokDknSbV9", "soUGkXq7Z4B28EZMai0k"}
		for _, voiceId := range voiceIds {
			err := worker.ElevenLabsDeleteVoice(context.Background(), &ElevenLabsDeleteVoiceRequest{
				VoiceID: voiceId,
			})
			fmt.Println(err)
			So(err, ShouldBeNil)
		}
	})
}
