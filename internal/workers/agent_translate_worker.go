package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/pkg/config"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

const (
	// agent翻译任务状态
	AgentTranslateTaskStatusSuccess    = "Success"    // 成功
	AgentTranslateTaskStatusFailed     = "Failed"     // 失败
	AgentTranslateTaskStatusProcessing = "Processing" // 执行中
	AgentTranslateTaskStatusCreated    = "Created"    // 任务已创建待执行
)

type AgentTranslateInputContent struct {
	FromLang          string                     `json:"from_lang"`          // 源语言代码（如 zh 表示中文）
	ToLang            string                     `json:"to_lang"`            // 目标语言代码（如 en 表示英文）
	Instruction       string                     `json:"instruction"`        // 用户自定义输入或具体任务描述
	Content           *AgentTranslateContentInfo `json:"content"`            // 原文
	IndexList         []int64                    `json:"index_list"`         // 要翻译的句子下标集合
	Translation       *AgentTranslateContentInfo `json:"translation"`        // 译文
	PhonemeAlign      bool                       `json:"phoneme_align"`      // 是否返回音素对齐信息
	CustomInstruction bool                       `json:"custom_instruction"` // 指令是否为预设内容
	TaskID            string                     `json:"task_id"`            // 任务ID
}

type AgentTranslateResult struct {
	Content     *AgentTranslateContentInfo `json:"content"`     // 原文
	Translation *AgentTranslateContentInfo `json:"translation"` // 译文
}

type AgentTranslateContentInfo struct {
	Subtitles []*AgentTranslateContentItem `json:"subtitles"` // 字幕内容
}

type AgentTranslateContentItem struct {
	ID   int64  `json:"id"`
	Text string `json:"text"`
}

type AgentTranslateOutputContentData struct {
	TaskID        string                `json:"task_id"`         // 任务ID
	Result        *AgentTranslateResult `json:"result"`          // 翻译结果
	Cost          float32               `json:"cost"`            // 价格
	Code          int32                 `json:"code"`            // 接口调用状态码
	CodeMsg       string                `json:"code_msg"`        // 接口调用状态码信息
	TaskStatus    string                `json:"task_status"`     // 任务状态
	TaskStatusMsg string                `json:"task_status_msg"` // 任务状态信息
}
type AgentTranslateWorker struct {
	*BaseWorker
	taskType  omniEngine.TaskType
	createUrl string
	resultUrl string
}

func NewAgentTranslateWorker(taskType omniEngine.TaskType) *AgentTranslateWorker {
	cfg := config.GetConfig()
	worker := NewBaseWorker()
	return &AgentTranslateWorker{
		BaseWorker: worker,
		taskType:   taskType,
		createUrl:  cfg.AgentTranslateService.CreateUrl,
		resultUrl:  cfg.AgentTranslateService.ResultUrl,
	}
}

func (w *AgentTranslateWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *AgentTranslateWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *AgentTranslateWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "AgentTranslateWorker Starting Agent Translate task: %d", t.Id)
	createReq := &AgentTranslateInputContent{}
	pbJsonBs, err := json.Marshal(t.GetAgentTranslateInputContent())
	if err != nil {
		g.Log().Errorf(ctx, "AgentTranslateWorker Failed to marshal error: %v, task: %v", err, t)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("AgentTranslateWorker Failed to marshal err: %v", err),
		}
	}
	err = json.Unmarshal(pbJsonBs, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "AgentTranslateWorker Failed to unmarshal error: %v, task: %v", err, t)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("AgentTranslateWorker Failed to unmarshal err: %v", err),
		}
	}
	createReq.TaskID = fmt.Sprintf("%d", t.Id)
	g.Log().Infof(ctx, "AgentTranslateWorker Parsed task request: %+v", createReq)
	createResp := &omniEngine.AgentTranslateOutputContentData{}
	if err := w.PostJSON(ctx, w.createUrl, createReq, createResp); err != nil {
		g.Log().Errorf(ctx, "AgentTranslateWorker Failed to create agent translate task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("AgentTranslateWorker Failed to create agent translate task: %v", err),
		}
	}
	if createResp.Code != 0 {
		g.Log().Errorf(ctx, "AgentTranslateWorker Failed to create agent translate task: %v", createResp)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: createResp.CodeMsg,
		}
	}
	g.Log().Infof(ctx, "AgentTranslateWorker Agent translate task created: %s", createReq.TaskID)
	return w.pollAgentTranslateTask(ctx, createReq.TaskID)
}

// 轮训等待代理翻译任务处理结果
func (w *AgentTranslateWorker) pollAgentTranslateTask(ctx context.Context, taskID string) (omniEngine.TaskStatus, *TaskOutputContent) {
	resultUrl := w.resultUrl + taskID
	pollingInterval := 1 * time.Second
	// 10分钟超时
	timeout := time.NewTimer(10 * time.Minute)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "AgentTranslateWorker Agent translate task timeout: %s", taskID)
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: fmt.Sprintf("Agent translate task timeout: %s", taskID),
			}
		default:
			resultResp := &omniEngine.AgentTranslateOutputContentData{}
			err := w.GetJSON(ctx, resultUrl, resultResp)
			if err != nil {
				g.Log().Errorf(ctx, "AgentTranslateWorker Failed to get agent translate task result: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Failed to get agent translate task result: %v", err),
				}
			}
			if resultResp.Code != 0 {
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    int(resultResp.Code),
					Message: resultResp.CodeMsg,
				}
			}
			// 使用select
			switch resultResp.TaskStatus {
			case AgentTranslateTaskStatusSuccess:
				g.Log().Infof(ctx, "AgentTranslateWorker Agent translate task completed: %s, status: %s, status_msg: %s", taskID, resultResp.TaskStatus, resultResp.TaskStatusMsg)
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    CodeCompleted,
					Message: StatusCompleted,
					Data:    resultResp,
				}
			case AgentTranslateTaskStatusFailed:
				g.Log().Errorf(ctx, "AgentTranslateWorker Agent translate task failed: %s, status: %s, status_msg: %s", taskID, resultResp.TaskStatus, resultResp.TaskStatusMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: resultResp.TaskStatusMsg,
				}
			case AgentTranslateTaskStatusCreated, AgentTranslateTaskStatusProcessing:
				g.Log().Infof(ctx, "AgentTranslateWorker Agent translate task is processing: %s, status: %s, status_msg: %s", taskID, resultResp.TaskStatus, resultResp.TaskStatusMsg)
				time.Sleep(pollingInterval)
				continue
			default:
				g.Log().Errorf(ctx, "AgentTranslateWorker Agent translate task is unknown: %s, status: %s, status_msg: %s", taskID, resultResp.TaskStatus, resultResp.TaskStatusMsg)
				// 未知状态
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Unknown agent translate task status: %s", resultResp.TaskStatus),
				}
			}
		}
	}
}
