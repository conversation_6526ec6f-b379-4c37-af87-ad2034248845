package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"omni_worker/pkg/config"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
)

type ElevenLabsTTSResp struct {
	ResultUrl string `json:"result_url"`
}

// https://api.elevenlabs.io/v1/voices/add

type ElevenLabsAddVoiceRequest struct {
	Name     string `json:"name"`
	FilePath string `json:"file_path"`
}

type ElevenLabsAddVoiceResponse struct {
	VoiceID string `json:"voice_id"`
}

type ElevenLabsErrorResponse struct {
	Detail struct {
		Status  string `json:"status"`
		Message string `json:"message"`
	} `json:"detail"`
}

func (w *ThirdPartyTTSWorker) ElevenLabsAddVoice(ctx context.Context, req *ElevenLabsAddVoiceRequest) (*ElevenLabsAddVoiceResponse, error) {
	responseBody := &ElevenLabsAddVoiceResponse{}
	cfg := config.GetConfig()
	err := retry.Do(func() error {
		resp, reqErr := w.restClient.R(ctx).
			SetContext(ctx).
			SetHeader("Xi-Api-Key", cfg.ElevenLabsConfig.ApiKey).
			SetHeader("Content-Type", "multipart/form-data").
			SetFormData(map[string]string{
				"name": req.Name,
			}).
			SetFiles(map[string]string{
				"files": req.FilePath,
			}).
			SetResult(responseBody).
			Post("https://api.elevenlabs.io/v1/voices/add")
		if reqErr != nil {
			g.Log().Warningf(ctx, "ElevenLabsAddVoice failed, err: %s", reqErr)
			return reqErr
		}
		if resp.StatusCode() != 200 {
			var errorResp ElevenLabsErrorResponse
			if unmarshalErr := json.Unmarshal(resp.Body(), &errorResp); unmarshalErr == nil {
				return fmt.Errorf("unexpected status code: %d, status: %s, message: %s, header:%s", resp.StatusCode(), errorResp.Detail.Status, errorResp.Detail.Message, resp.Header())
			}
			return fmt.Errorf("unexpected status code: %d, header:%s", resp.StatusCode(), resp.Header())
		}
		return nil
	},
		retry.OnRetry(func(n uint, err error) {
			g.Log().Warningf(ctx, "ElevenLabsAddVoice failed, retry:%d, err: %s", n, err)
		}),
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(1*time.Minute),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		return nil, err
	}
	return responseBody, nil
}

// https://api.elevenlabs.io/v1/text-to-speech/:voice_id

type ElevenLabsTextToSpeechRequest struct {
	VoiceID      string `json:"voice_id"`
	Text         string `json:"text"`
	LanguageCode string `json:"language_code,omitempty"`
	ModelID      string `json:"model_id"`
}

func (w *ThirdPartyTTSWorker) ElevenLabsTextToSpeech(ctx context.Context, req *ElevenLabsTextToSpeechRequest) (string, error) {
	cfg := config.GetConfig()
	// 创建临时文件
	tempFile, err := os.CreateTemp("", "11labs_tts_*.mp3")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %w", err)
	}
	tempFilePath := tempFile.Name()
	tempFile.Close()
	err = retry.Do(func() error {
		resp, reqErr := w.restClient.R(ctx).
			SetContext(ctx).
			SetHeader("Xi-Api-Key", cfg.ElevenLabsConfig.ApiKey).
			SetHeader("Content-Type", "application/json").
			SetBody(req).
			Post(fmt.Sprintf("https://api.elevenlabs.io/v1/text-to-speech/%s", req.VoiceID))
		if reqErr != nil {
			g.Log().Warningf(ctx, "ElevenLabsTextToSpeech failed, err: %s", reqErr)
			return reqErr
		}
		if resp.StatusCode() != 200 {
			var errorResp ElevenLabsErrorResponse
			if unmarshalErr := json.Unmarshal(resp.Body(), &errorResp); unmarshalErr == nil {
				return fmt.Errorf("unexpected status code: %d, status: %s, message: %s, header:%s", resp.StatusCode(), errorResp.Detail.Status, errorResp.Detail.Message, resp.Header())
			}
			return fmt.Errorf("unexpected status code: %d, header:%s", resp.StatusCode(), resp.Header())
		}
		writeErr := os.WriteFile(tempFilePath, resp.Body(), 0o644)
		if writeErr != nil {
			return fmt.Errorf("failed to write temp file: %w", writeErr)
		}
		return nil
	},
		retry.OnRetry(func(n uint, err error) {
			g.Log().Warningf(ctx, "ElevenLabsTextToSpeech failed, retry:%d, err: %s", n, err)
		}),
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(1*time.Minute),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		return "", err
	}
	return tempFilePath, nil
}

// https://api.elevenlabs.io/v1/voices/:voice_id

type ElevenLabsDeleteVoiceRequest struct {
	VoiceID string `json:"voice_id"`
}

func (w *ThirdPartyTTSWorker) ElevenLabsDeleteVoice(ctx context.Context, req *ElevenLabsDeleteVoiceRequest) error {
	cfg := config.GetConfig()
	err := retry.Do(func() error {
		resp, reqErr := w.restClient.R(ctx).
			SetContext(ctx).
			SetHeader("Xi-Api-Key", cfg.ElevenLabsConfig.ApiKey).
			Delete(fmt.Sprintf("https://api.elevenlabs.io/v1/voices/%s", req.VoiceID))
		if reqErr != nil {
			g.Log().Warningf(ctx, "ElevenLabsDeleteVoice failed, err: %s", reqErr)
			return reqErr
		}
		if resp.StatusCode() != 200 {
			var errorResp ElevenLabsErrorResponse
			if unmarshalErr := json.Unmarshal(resp.Body(), &errorResp); unmarshalErr == nil {
				return fmt.Errorf("unexpected status code: %d, status: %s, message: %s, header:%s", resp.StatusCode(), errorResp.Detail.Status, errorResp.Detail.Message, resp.Header())
			}
			return fmt.Errorf("unexpected status code: %d, header:%s", resp.StatusCode(), resp.Header())
		}
		return nil
	},
		retry.OnRetry(func(n uint, err error) {
			g.Log().Warningf(ctx, "ElevenLabsDeleteVoice failed, retry:%d, err: %s", n, err)
		}),
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(1*time.Minute),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		return err
	}
	return nil
}
