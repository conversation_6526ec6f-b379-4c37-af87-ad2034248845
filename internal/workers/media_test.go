package workers

import (
	"omni_worker/pkg/media"
	"testing"
)

func TestGetAudioVideoInfo(t *testing.T) {
	inputFile := "D:\\dwww\\subtitle\\4.01.mp4"
	info, err := media.GetAudioVideoInfo(inputFile)
	if err != nil {
		t.<PERSON><PERSON>("GetAudioVideoInfo failed: %v", err)
		return
	}
	t.Logf("GetAudioVideoInfo success: %+v", info)
}

func TestMergeSubtitle(t *testing.T) {
	err := media.CombineVideoAndSubtitlesWithPreset(0, "D:\\dwww\\subtitle\\4.0100.mp4", "", "", "", "21",
		"ultrafast", 8, false, true)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("CombineVideoAndSubtitlesWithPreset failed: %v", err)
		return
	}
	t.Log("CombineVideoAndSubtitlesWithPreset success")
}
