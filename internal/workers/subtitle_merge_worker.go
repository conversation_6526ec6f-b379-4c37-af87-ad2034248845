package workers

import (
	"context"
	"encoding/json"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/internal/util"
	"omni_worker/pkg/config"
	"omni_worker/pkg/media"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type SubtitleMergeSubmitReq struct {
	TaskId           int64   `json:"task_id"`
	SubtitleId       int64   `json:"subtitle_id"`
	VocalAudioPath   string  `json:"vocal_audio_path"`
	AssFilePath      string  `json:"ass_file_path"`
	IsWaterMark      bool    `json:"is_water_mark"`
	IsVocalTask      bool    `json:"is_vocal_task"`
	NotitleVideoPath string  `json:"notitle_video_path"`
	TargetObjectName string  `json:"target_object_name"`
	PostVideoPath    string  `json:"post_video_path"` // 合成配音后的视频路径
	VideoDuration    float32 `json:"video_duration"`  // 视频时长，单位：秒
	Crf              string  `json:"crf"`
	Preset           string  `json:"preset"`
}

type SubtitleMergeEngineRes struct {
	TaskId             int64  `json:"task_id"`
	SubtitleId         int64  `json:"subtitle_id"`
	MergeStartAt       int64  `json:"merge_start_at"`
	MergeEndAt         int64  `json:"merge_end_at"`
	MergeUploadStartAt int64  `json:"merge_upload_start_at"`
	MergeUploadEndAt   int64  `json:"merge_upload_end_at"`
	RetCode            int    `json:"ret_code"`
	ErrMsg             string `json:"err_msg"`
}

type SubtitleMergeSubmitResp struct {
	TaskId  int64  `json:"task_id"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type SubtitleMergeWorker struct {
	*BaseWorker
	taskType         omniEngine.TaskType
	ffmpegThreadCore int
}

func NewSubtitleMergeWorker(taskType omniEngine.TaskType) *SubtitleMergeWorker {
	if err := media.MediaInit(); err != nil {
		g.Log().Errorf(context.Background(), "subtitle merge worker init failed, err: %v", err)
		return nil
	}
	cfg := config.GetConfig()
	return &SubtitleMergeWorker{
		BaseWorker:       NewBaseWorker(),
		taskType:         taskType,
		ffmpegThreadCore: cfg.SubtitleMergeServiceConfig.MergeThreadCore,
	}
}

func (w *SubtitleMergeWorker) Process(ctx context.Context, task *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &SubtitleMergeSubmitReq{}
	err := json.Unmarshal([]byte(task.CommonInputContent), req)
	if err != nil {
		g.Log().Error(ctx, "subtitle merge submit req unmarshal failed, engineId: %v err: %v",
			task.Id, err)
		return omniEngine.TaskStatus_FAILED, nil
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker Process start, taskId: %v, req: %+v", task.Id, *req)
	return w.doSubmitSubtitleMerge(ctx, task, req)
}

func (w *SubtitleMergeWorker) doSubmitSubtitleMerge(ctx context.Context, task *omniEngine.Task, req *SubtitleMergeSubmitReq) (
	omniEngine.TaskStatus, *TaskOutputContent,
) {
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, taskId: %v, subtitleId: %v, engineId: %v",
		req.TaskId, req.SubtitleId, task.Id)
	// 合成视频
	// 上传视频
	// 返回结果
	res := &SubtitleMergeEngineRes{}
	res.RetCode = consts.CommonCodeFail
	res.TaskId = req.TaskId
	res.SubtitleId = req.SubtitleId
	res.MergeStartAt = time.Now().UnixMilli()
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, start merge, taskId: %v, SubtitleId: %v, engineId: %v",
		req.TaskId, req.SubtitleId, task.Id)

	// 设置超时时间
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Hour)
	defer cancel()
	wg := concurrent.New(timeoutCtx, 1)
	wg.Go(func() error {
		return media.CombineVideoAndSubtitlesWithPreset(req.TaskId, req.NotitleVideoPath, req.VocalAudioPath, req.AssFilePath, req.PostVideoPath,
			req.Crf, req.Preset, w.ffmpegThreadCore, req.IsWaterMark, req.IsVocalTask)
	})
	err := wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge ffmpeg failed, err: %v", err)
		res.ErrMsg = err.Error()
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "subtitle merge ffmpeg failed." + res.ErrMsg,
			Data:    res,
		}
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, merge over, taskId: %v, SubtitleId: %v, engineId: %v",
		req.TaskId, req.SubtitleId, task.Id)
	res.MergeEndAt = time.Now().UnixMilli()
	// 文件是否存在？
	err = util.EnsureFile(req.PostVideoPath)
	if err != nil {
		g.Log().Errorf(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge file not found, err: %v", err)
		res.ErrMsg = err.Error()
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "subtitle merge file not found." + res.ErrMsg,
			Data:    res,
		}
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, start upload, taskId: %v, SubtitleId: %v, engineId: %v",
		req.TaskId, req.SubtitleId, task.Id)
	// 上传文件
	res.MergeUploadStartAt = time.Now().UnixMilli()
	err = util.UploadAndEnsureMediaFile(req.PostVideoPath, req.TargetObjectName)
	if err != nil {
		g.Log().Errorf(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge upload failed, taskId: %v, subtitleId: %v, engineId: %v, err: %v",
			req.TaskId, req.SubtitleId, task.Id, err)
		res.ErrMsg = err.Error()
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "subtitle merge upload failed." + res.ErrMsg,
			Data:    res,
		}
	}
	g.Log().Infof(ctx, "SubtitleMergeWorker doSubmitSubtitleMerge, end upload, taskId: %v, SubtitleId: %v, engineId: %v",
		req.TaskId, req.SubtitleId, task.Id)
	res.MergeUploadEndAt = time.Now().UnixMilli()
	return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
		Code:    consts.CommonCodeOk,
		Message: "success",
		Data:    res,
	}
}

func (w *SubtitleMergeWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *SubtitleMergeWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}
