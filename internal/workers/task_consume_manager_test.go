package workers

import (
	"context"
	"testing"

	"omni_worker/pkg/config"

	"github.com/stretchr/testify/assert"
)

func TestGetNextCluster(t *testing.T) {
	ctx := context.Background()
	// 测试用例1: 常规调度
	t.Run("正常轮转调度", func(t *testing.T) {
		// 创建配置
		cfg := &config.TaskConsumeConfig{
			Enable: true,
			TaskConsumeRuleList: []*config.TaskConsumeRule{
				{Cluster: "cluster1", Count: 2},
				{Cluster: "cluster2", Count: 3},
			},
		}

		// 初始化manager
		manager := newTaskConsumeManager(ctx, cfg)

		// 预期的调度顺序
		expectedOrder := []string{
			"cluster1", "cluster1", // cluster1 消费2次
			"cluster2", "cluster2", "cluster2", // cluster2 消费3次
			// 重置后再次开始
			"cluster1", "cluster1", // cluster1 又消费2次
			"cluster2", "cluster2", "cluster2", // cluster2 又消费3次
		}

		// 验证调度结果
		for i, expected := range expectedOrder {
			result := manager.getNextCluster()
			assert.Equal(t, expected, result, "第%d次调度结果不匹配", i+1)
		}
	})

	// 测试用例2: manager为nil或enable为false
	t.Run("管理器无效", func(t *testing.T) {
		var manager *taskConsumeManager
		result := manager.getNextCluster()
		assert.Equal(t, "", result, "manager为nil时应返回空字符串")

		manager = &taskConsumeManager{enable: false}
		result = manager.getNextCluster()
		assert.Equal(t, "", result, "enable为false时应返回空字符串")
	})

	// 测试用例3: taskConsumeCtx为nil或规则列表为空
	t.Run("上下文无效", func(t *testing.T) {
		manager := &taskConsumeManager{
			enable:         true,
			taskConsumeCtx: nil,
		}
		result := manager.getNextCluster()
		assert.Equal(t, "", result, "taskConsumeCtx为nil时应返回空字符串")

		manager = &taskConsumeManager{
			enable: true,
			taskConsumeCtx: &taskConsumeCtx{
				taskConsumeRuleList: []*taskConsumeRule{},
			},
		}
		result = manager.getNextCluster()
		assert.Equal(t, "", result, "规则列表为空时应返回空字符串")
	})

	// 测试用例4: 规则的Count耗尽后能否正确跳到下一个规则
	t.Run("规则Count耗尽时切换", func(t *testing.T) {
		cfg := &config.TaskConsumeConfig{
			Enable: true,
			TaskConsumeRuleList: []*config.TaskConsumeRule{
				{Cluster: "cluster1", Count: 1},
				{Cluster: "cluster2", Count: 1},
			},
		}

		manager := newTaskConsumeManager(ctx, cfg)

		// 第一次获取应该是cluster1
		result := manager.getNextCluster()
		assert.Equal(t, "cluster1", result)

		// 第二次获取应该是cluster2
		result = manager.getNextCluster()
		assert.Equal(t, "cluster2", result)

		// 第三次获取应该重置后返回cluster1
		result = manager.getNextCluster()
		assert.Equal(t, "cluster1", result)
	})

	// 测试用例5: 所有规则的Count为0时能否正确重置
	t.Run("Count全为0时重置", func(t *testing.T) {
		// 创建配置但手动修改count为0
		cfg := &config.TaskConsumeConfig{
			Enable: true,
			TaskConsumeRuleList: []*config.TaskConsumeRule{
				{Cluster: "cluster1", Count: 2},
				{Cluster: "cluster2", Count: 1},
			},
		}

		manager := newTaskConsumeManager(ctx, cfg)

		// 手动设置所有Count为0以模拟所有规则都用尽的情况
		manager.taskConsumeCtx.taskConsumeRuleList[0].Count = 0
		manager.taskConsumeCtx.taskConsumeRuleList[1].Count = 0

		// 现在获取应该触发重置并返回cluster1
		result := manager.getNextCluster()
		assert.Equal(t, "cluster1", result)

		// 验证下一次也能正确返回
		result = manager.getNextCluster()
		assert.Equal(t, "cluster1", result)

		// 然后应该返回cluster2
		result = manager.getNextCluster()
		assert.Equal(t, "cluster2", result)
	})
}
