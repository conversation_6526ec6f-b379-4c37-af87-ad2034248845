package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/pkg/config"

	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
)

type VideoAlignmentReq struct {
	TaskID              int64          `json:"task_id"`
	AudioInfo           []*AudioDetail `json:"audio_info"`
	UnderstandingResult string         `json:"understanding_result"`
	VideoDuration       float64        `json:"video_duration"`
}

type AudioDetail struct {
	SegmentID       int64   `json:"segment_id"`
	NarrationScript string  `json:"narration_script"`
	TargetText      string  `json:"target_text"`
	TTSURL          string  `json:"tts_url"`
	TTSDuration     float64 `json:"tts_duration"`
}

type VideoAlignmentRes struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    *VideoAlignmentData `json:"data"`
}

type VideoAlignmentData struct {
	TaskID          int64            `json:"task_id"`
	AlignmentResult []*AlignmentInfo `json:"alignment_result"`
}

type AlignmentInfo struct {
	AudioDetail
	VideoSegments []*VideoSegment `json:"video_segments"`
}

type VideoSegment struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

type VideoAlignmentWorker struct {
	*BaseWorker
	VideoAlignmentCreateUrl string
	VideoAlignmentQueryUrl  string
	taskType                omniEngine.TaskType
}

func NewVideoAlignmentWorker(taskType omniEngine.TaskType) *VideoAlignmentWorker {
	cfg := config.GetConfig()
	return &VideoAlignmentWorker{
		BaseWorker:              NewBaseWorker(),
		VideoAlignmentCreateUrl: cfg.VideoTranslateService.EraseV2CreateUrl,
		VideoAlignmentQueryUrl:  cfg.VideoTranslateService.EraseV2QueryUrl,
		taskType:                taskType,
	}
}

func (w *VideoAlignmentWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *VideoAlignmentWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *VideoAlignmentWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &VideoAlignmentReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "VideoAlignmentWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleVideoAlignmentTask(ctx, t, req)
}

func (w *VideoAlignmentWorker) handleVideoAlignmentTask(ctx context.Context, t *omniEngine.Task, req *VideoAlignmentReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleVideoAlignmentTask] start task, engineId: %v", req.TaskID)
	cfg := config.GetConfig()
	timeoutInterval := time.Duration(cfg.VideoAlignmentConfig.TimeoutSeconds) * time.Second
	submitTaskInterval := time.Duration(cfg.VideoAlignmentConfig.SubmitTaskIntervalMs) * time.Millisecond
	timeout := time.NewTimer(timeoutInterval)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			errorMsg := fmt.Sprintf("task submit timeout:%fs, taskId:%d", timeoutInterval.Seconds(), req.TaskID)
			g.Log().Errorf(ctx, errorMsg)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: errorMsg,
			}
		default:
			resp := &VideoAlignmentRes{}
			err := retry.Do(func() error {
				return w.PostJSON(ctx, cfg.VideoAlignmentConfig.CreateUrl, req, resp)
			},
				retry.Attempts(3),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				errorMsg := fmt.Sprintf("create task err: %s", err)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
			switch resp.Code {
			case CodeSuccess:
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    CodeSuccess,
					Message: resp.Message,
					Data:    resp,
				}
			case CodeAlgorithmBusy:
				time.Sleep(submitTaskInterval)
			default:
				errorMsg := fmt.Sprintf("create task err, code: %d, message: %s", resp.Code, resp.Message)
				g.Log().Errorf(ctx, errorMsg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: errorMsg,
				}
			}
		}
	}
}
