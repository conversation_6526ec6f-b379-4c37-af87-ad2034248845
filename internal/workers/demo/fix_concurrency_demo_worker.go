package demo

import (
	"context"
	"encoding/json"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/internal/workers"
	"omni_worker/pkg/config"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/gogf/gf/v2/frame/g"
)

// 并发worker Demo
type FixConcurrencyDemoReq struct{}

type FixConcurrencyDemoRes struct{}

type FixConcurrencyDemoQueryReq struct{}

type FixConcurrencyDemoQueryRes struct{}

type FixConcurrencyDemoWorker struct {
	*workers.BaseWorker
	FixConcurrencyDemoCreateUrl string
	FixConcurrencyDemoQueryUrl  string
	taskType                    omniEngine.TaskType
}

func NewFixConcurrencyDemoWorker(taskType omniEngine.TaskType) *FixConcurrencyDemoWorker {
	cfg := config.GetConfig()
	return &FixConcurrencyDemoWorker{
		BaseWorker:                  workers.NewBaseWorker(),
		FixConcurrencyDemoCreateUrl: cfg.VideoTranslateService.EraseV2CreateUrl,
		FixConcurrencyDemoQueryUrl:  cfg.VideoTranslateService.EraseV2QueryUrl,
		taskType:                    taskType,
	}
}

func (w *FixConcurrencyDemoWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *FixConcurrencyDemoWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}

func (w *FixConcurrencyDemoWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	req := &FixConcurrencyDemoReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "FixConcurrencyDemoWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleFixConcurrencyDemoTask(ctx, t, req)
}

func (w *FixConcurrencyDemoWorker) handleFixConcurrencyDemoTask(ctx context.Context, t *omniEngine.Task, req *FixConcurrencyDemoReq) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	g.Log().Infof(ctx, "[handleFixConcurrencyDemoTask] start task, engineId: %v", t.Id)
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Hour)
	defer cancel()
	wg := concurrent.New(timeoutCtx, 1)
	var res FixConcurrencyDemoRes
	wg.Go(func() error {
		return w.PostJSON(timeoutCtx, w.FixConcurrencyDemoCreateUrl, req, &res)
	})
	err := wg.Wait()
	if err != nil {
		g.Log().Errorf(ctx, "[handleFixConcurrencyDemoTask] process task failed, err: %v", err)
		return omniEngine.TaskStatus_FAILED, &workers.TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: "process task failed, err: " + err.Error(),
		}
	}
	return omniEngine.TaskStatus_COMPLETED, &workers.TaskOutputContent{
		Code:    consts.CommonCodeOk,
		Message: "process task success",
		Data:    res,
	}
}
