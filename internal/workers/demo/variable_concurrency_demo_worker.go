package demo

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/internal/workers"
	"omni_worker/pkg/config"

	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
)

// 并发worker Demo
type VariableConcurrencyDemoReq struct{}

type VariableConcurrencyDemoRes struct{}

type VariableConcurrencyDemoQueryReq struct{}

type VariableConcurrencyDemoQueryRes struct{}

type VariableConcurrencyDemoWorker struct {
	*workers.BaseWorker
	VariableConcurrencyDemoCreateUrl string
	VariableConcurrencyDemoQueryUrl  string
	taskType                         omniEngine.TaskType
}

func NewVariableConcurrencyDemoWorker(taskType omniEngine.TaskType) *VariableConcurrencyDemoWorker {
	cfg := config.GetConfig()
	return &VariableConcurrencyDemoWorker{
		BaseWorker:                       workers.NewBaseWorker(),
		VariableConcurrencyDemoCreateUrl: cfg.VideoTranslateService.EraseV2CreateUrl,
		VariableConcurrencyDemoQueryUrl:  cfg.VideoTranslateService.EraseV2QueryUrl,
		taskType:                         taskType,
	}
}

func (w *VariableConcurrencyDemoWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *VariableConcurrencyDemoWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	req := &VariableConcurrencyDemoReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "VariableConcurrencyDemoWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.query(ctx, t, req)
}

func (w *VariableConcurrencyDemoWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	req := &VariableConcurrencyDemoReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "VariableConcurrencyDemoWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleVariableConcurrencyDemoTask(ctx, t, req)
}

func (w *VariableConcurrencyDemoWorker) handleVariableConcurrencyDemoTask(ctx context.Context, t *omniEngine.Task, req *VariableConcurrencyDemoReq) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	g.Log().Infof(ctx, "[handleVariableConcurrencyDemoTask] start task, engineId: %v", t.Id)
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[handleVariableConcurrencyDemoTask] timeout, engineId: %v", t.Id)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &workers.TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "task submit timeout",
			}
		default:
			status, res := w.submitTask(ctx, t, req)
			if status == omniEngine.TaskStatus_NOT_STARTED {
				time.Sleep(time.Second)
			} else {
				return status, res
			}
		}
	}
}

func (w *VariableConcurrencyDemoWorker) submitTask(ctx context.Context, t *omniEngine.Task, req *VariableConcurrencyDemoReq) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	res := &VariableConcurrencyDemoRes{}
	err := retry.Do(func() error {
		return w.PostJSON(ctx, w.VariableConcurrencyDemoCreateUrl, req, res)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "[submitTask] HTTP req failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &workers.TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("http req failed:%s", err),
		}
	}
	return omniEngine.TaskStatus_IN_PROGRESS, &workers.TaskOutputContent{
		Code:    consts.CommonCodeOk,
		Message: "ok",
		Data:    res,
	}
}

func (w *VariableConcurrencyDemoWorker) query(ctx context.Context, t *omniEngine.Task, req *VariableConcurrencyDemoReq) (omniEngine.TaskStatus, *workers.TaskOutputContent) {
	// 构建查询请求
	queryReq := &VariableConcurrencyDemoQueryReq{}
	resp := &VariableConcurrencyDemoQueryRes{}
	beginTime := time.Now()
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[query] timeout, engineId: %v, beginTime: %v",
				t.Id, beginTime.Format(time.DateTime))
			return omniEngine.TaskStatus_FAILED, &workers.TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "query result timeout",
			}
		default:
			err := retry.Do(func() error {
				return w.PostJSON(ctx, w.VariableConcurrencyDemoQueryUrl, queryReq, resp)
			},
				retry.Attempts(5),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "[query] http request failed: %v", err)
				return omniEngine.TaskStatus_FAILED, &workers.TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("http request failed:%s", err),
				}
			}
			g.Log().Infof(ctx, "[query] query ok, engineId: %v", t.Id)
			return omniEngine.TaskStatus_IN_PROGRESS, &workers.TaskOutputContent{
				Code:    consts.CommonCodeOk,
				Message: "ok",
				Data:    resp,
			}
		}
	}
}
