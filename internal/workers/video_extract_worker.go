package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	"github.com/avast/retry-go"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

// 提取请求
type ExtractReq struct {
	Id           int64     `json:"id"`
	VideoUrl     string    `json:"video_url"`
	Regions      []float64 `json:"selected_region"`
	StartMSecond int64     `json:"start_mseconds"`
	EndMSecond   int64     `json:"end_mseconds"`
	FullScreen   bool      `json:"full_screen"`
	Language     string    `json:"language"`
}

type ExtractRes struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

// 提取查询请求
type ExtractQueryReq struct {
	Id int64 `json:"id"`
}

type OcrExtractTextItem struct {
	Start int32  `json:"start"` // 字幕开始帧
	End   int32  `json:"end"`   // 字幕结束帧
	Texts string `json:"texts"` // 字幕
}

type ExtractQueryRes struct {
	Code        int32                `json:"code"`
	Msg         string               `json:"message"`
	Id          int64                `json:"id"`
	Spent       int64                `json:"spent"`
	Regions     [][]float64          `json:"region"`
	Fps         float32              `json:"fps"`          // 视频帧率
	Width       int32                `json:"width"`        // 视频宽
	Height      int32                `json:"height"`       // 视频高
	FrameCount  int32                `json:"frame_count"`  // 视频总帧数
	MergedTexts []OcrExtractTextItem `json:"merged_texts"` // 视频字幕
	BBoxesFile  []BBoxes             `json:"bboxes_file"`  // 字幕检测的详细信息
}

// https://q9jvw0u5f5.feishu.cn/wiki/NrF7wmJxlivSMYkaaLTcR8MInac#JETmd5QJXopkFyx7pzjc2QxCnKe
type ExtractV2Worker struct {
	*BaseWorker
	ExtractV2CreateUrl string
	ExtractV2QueryUrl  string
	taskType           omniEngine.TaskType
}

func NewExtractV2Worker(taskType omniEngine.TaskType) *ExtractV2Worker {
	cfg := config.GetConfig()
	return &ExtractV2Worker{
		BaseWorker:         NewBaseWorker(),
		ExtractV2CreateUrl: cfg.VideoTranslateService.ExtractV2CreateUrl,
		ExtractV2QueryUrl:  cfg.VideoTranslateService.ExtractV2QueryUrl,
		taskType:           taskType,
	}
}

func (w *ExtractV2Worker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *ExtractV2Worker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &ExtractReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "ExtractV2Worker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.query(ctx, t, req)
}

func (w *ExtractV2Worker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &ExtractReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "ExtractV2Worker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.handleExtractTask(ctx, t, req)
}

func (w *ExtractV2Worker) handleExtractTask(ctx context.Context, t *omniEngine.Task, req *ExtractReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleExtractTask] start task, engineId: %v, taskId: %v", t.Id, req.Id)
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[handleExtractTask] timeout, engineId:%d, taskId: %v", t.Id, req.Id)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    0,
				Message: "task submit timeout",
			}
		default:
			status, res := w.submitTask(ctx, t, req)
			if status == omniEngine.TaskStatus_NOT_STARTED {
				time.Sleep(time.Second)
			} else {
				return status, res
			}
		}
	}
}

func (w *ExtractV2Worker) submitTask(ctx context.Context, t *omniEngine.Task, req *ExtractReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	res := &ExtractRes{}
	err := retry.Do(func() error {
		return w.PostJSON(ctx, w.ExtractV2CreateUrl, req, res)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "[submitTask] HTTP req failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("http req failed:%s", err),
		}
	}
	switch res.Code {
	case consts.AiCodeOk:
		g.Log().Infof(ctx, "[submitTask] submit ok, engineId: %v, taskId: %v", t.Id, req.Id)
		return omniEngine.TaskStatus_IN_PROGRESS, &TaskOutputContent{
			Code:    consts.CommonCodeOk,
			Message: "ok",
			Data:    res,
		}
	case consts.AiCodeBusy:
		// 预处理繁忙，继续尝试提交
		g.Log().Infof(ctx, "[submitTask] extract busy, retry commit, msg:%s, id: %v, engineId: %v", res.Msg, req.Id, t.Id)
		return omniEngine.TaskStatus_NOT_STARTED, nil
	case consts.AiCodeParamsErr:
		g.Log().Errorf(ctx, "[submitTask] task failed, err:%s, content: %+v", res.Msg, req)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("task failed:%s", res.Msg),
			Data:    res,
		}
	default:
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("unknown code:%d", res.Code),
			Data:    res,
		}
	}
}

func (w *ExtractV2Worker) query(ctx context.Context, t *omniEngine.Task, req *ExtractReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 构建查询请求
	queryReq := &ExtractQueryReq{Id: req.Id}
	resp := &ExtractQueryRes{}
	beginTime := time.Now()
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	retryCount := 0
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[query] timeout, engineId: %v, taskId: %v, beginTime: %v",
				t.Id, req.Id, beginTime.Format(time.DateTime))
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "query result timeout",
			}
		default:
			err := retry.Do(func() error {
				return w.PostJSON(ctx, w.ExtractV2QueryUrl, queryReq, resp)
			},
				retry.Attempts(5),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "[query] http request failed: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("http request failed:%s", err),
				}
			}
			switch resp.Code {
			case consts.AiCodeOk:
				g.Log().Infof(ctx, "[query] task completed, engineId: %v, taskId: %v, timeCost: %v",
					t.Id, req.Id, time.Since(beginTime).Seconds())
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    consts.CommonCodeOk,
					Message: "ok",
					Data:    resp,
				}
			case consts.AiCodeBusy:
				g.Log().Infof(ctx, "[query] extract in progress, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				time.Sleep(time.Second)
			case consts.AiCodeTaskNotExist, consts.AiCodeCudaOutOfMemory:
				g.Log().Infof(ctx, "[query] retry submit pro task, msg:%s, taskId: %v, engineId: %v", resp.Msg, req.Id, t.Id)
				retryCount++
				if retryCount > 3 {
					return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
						Code:    consts.CommonCodeFail,
						Message: "attempt all retry failed",
					}
				}
				// 任务不存在或者内存不足，需要重新提交
				status, res := w.handleExtractTask(ctx, t, req)
				// 重新提交成功，继续查询结果
				if status == omniEngine.TaskStatus_IN_PROGRESS {
					continue
				}
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: "retry submit pro task failed",
					Data:    res,
				}
			case consts.AiCodePullVideoFailed, consts.AiCodeParamsErr,
				consts.AiCodeInternalError:
				g.Log().Infof(ctx, "[query] task failed, taskId: %v, engineId: %v, code: %v, msg: %s",
					req.Id, t.Id, resp.Code, resp.Msg)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: resp.Msg,
					Data:    resp,
				}
			default:
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("unknown code:%d", resp.Code),
					Data:    resp,
				}
			}
		}
	}
}
