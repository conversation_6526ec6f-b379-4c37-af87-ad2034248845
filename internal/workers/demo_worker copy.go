package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/internal/consts"
	"omni_worker/pkg/config"

	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
)

// 并发worker demo
type DemoReq struct{}

type DemoRes struct{}

type DemoQueryReq struct{}

type DemoQueryRes struct{}

type DemoWorker struct {
	*BaseWorker
	DemoCreateUrl string
	DemoQueryUrl  string
	taskType      omniEngine.TaskType
}

func NewDemoWorker(taskType omniEngine.TaskType) *DemoWorker {
	cfg := config.GetConfig()
	return &DemoWorker{
		BaseWorker:    NewBaseWorker(),
		DemoCreateUrl: cfg.VideoTranslateService.EraseV2CreateUrl,
		DemoQueryUrl:  cfg.VideoTranslateService.EraseV2QueryUrl,
		taskType:      taskType,
	}
}

func (w *DemoWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *DemoWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &DemoReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "DemoWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}
	return w.query(ctx, t, req)
}

func (w *DemoWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	req := &DemoReq{}
	err := json.Unmarshal([]byte(t.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "DemoWorker unmarshal failed, err:%s, content: %s", err, t.CommonInputContent)
		return omniEngine.TaskStatus_FAILED, nil
	}

	return w.handleDemoTask(ctx, t, req)
}

func (w *DemoWorker) handleDemoTask(ctx context.Context, t *omniEngine.Task, req *DemoReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "[handleDemoTask] start task, engineId: %v", t.Id)
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[handleDemoTask] timeout, engineId: %v", t.Id)
			return omniEngine.TaskStatus_QUEUING_TIMEOUT, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "task submit timeout",
			}
		default:
			status, res := w.submitTask(ctx, t, req)
			if status == omniEngine.TaskStatus_NOT_STARTED {
				time.Sleep(time.Second)
			} else {
				return status, res
			}
		}
	}
}

func (w *DemoWorker) submitTask(ctx context.Context, t *omniEngine.Task, req *DemoReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	res := &DemoRes{}
	err := retry.Do(func() error {
		return w.PostJSON(ctx, w.DemoCreateUrl, req, res)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay), // 指数退避
	)
	if err != nil {
		g.Log().Errorf(ctx, "[submitTask] HTTP req failed: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    consts.CommonCodeFail,
			Message: fmt.Sprintf("http req failed:%s", err),
		}
	}
	return omniEngine.TaskStatus_IN_PROGRESS, &TaskOutputContent{
		Code:    consts.CommonCodeOk,
		Message: "ok",
		Data:    res,
	}
}

func (w *DemoWorker) query(ctx context.Context, t *omniEngine.Task, req *DemoReq) (omniEngine.TaskStatus, *TaskOutputContent) {
	// 构建查询请求
	queryReq := &DemoQueryReq{}
	resp := &DemoQueryRes{}
	beginTime := time.Now()
	timeout := time.NewTimer(time.Duration(3600) * time.Second)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "[query] timeout, engineId: %v, beginTime: %v",
				t.Id, beginTime.Format(time.DateTime))
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    consts.CommonCodeFail,
				Message: "query result timeout",
			}
		default:
			err := retry.Do(func() error {
				return w.PostJSON(ctx, w.DemoQueryUrl, queryReq, resp)
			},
				retry.Attempts(5),
				retry.Delay(time.Second),
				retry.MaxDelay(30*time.Second),
				retry.DelayType(retry.BackOffDelay), // 指数退避
			)
			if err != nil {
				g.Log().Errorf(ctx, "[query] http request failed: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    consts.CommonCodeFail,
					Message: fmt.Sprintf("http request failed:%s", err),
				}
			}
			g.Log().Infof(ctx, "[query] query ok, engineId: %v", t.Id)
			return omniEngine.TaskStatus_IN_PROGRESS, &TaskOutputContent{
				Code:    consts.CommonCodeOk,
				Message: "ok",
				Data:    resp,
			}
		}
	}
}
