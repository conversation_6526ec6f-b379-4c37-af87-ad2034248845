package workers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"omni_worker/internal/util"
	"omni_worker/pkg/config"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

type TextTranslateWorker struct {
	*BaseWorker
	taskType            omniEngine.TaskType
	createUrl           string
	resultUrl           string
	checkResultInterval int // 检查结果的时间间隔（毫秒）
}

func NewTextTranslateWorker(taskType omniEngine.TaskType) *TextTranslateWorker {
	cfg := config.GetConfig().TextTranslateService
	checkResultInterval := cfg.CheckResultIntervalMs
	if checkResultInterval == 0 {
		checkResultInterval = 300
	}
	return &TextTranslateWorker{
		BaseWorker:          NewBaseWorker(),
		taskType:            taskType,
		createUrl:           cfg.CreateUrl,
		resultUrl:           cfg.ResultUrl,
		checkResultInterval: checkResultInterval,
	}
}

type ParseSrtResult struct {
	Items []*util.SrtItem
}

type ParseTextResult struct {
	Text string
}

type TranslateProcessResp struct {
	Items        []*util.SrtItem `json:"items"`
	EngineTaskID int64           `json:"engine_task_id"`
}

type TranslateProcessRespData struct{}

type TextTransReq[T ParseTextResult | ParseSrtResult] struct {
	Data     T      `json:"data"`
	TaskID   int64  `json:"task_id"`
	From     string `json:"from"`
	To       string `json:"to"`
	AppId    int64  `json:"app_id"`
	TenantId int64  `json:"tenant_id"`
}

type TextTransQueryReq struct {
	EngineTaskID int64 `json:"engine_task_id"`
	AppId        int64 `json:"app_id"`
	TenantId     int64 `json:"tenant_id"`
}

type TextTransQueryResp struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    TextTransQueryRespData `json:"data"`
}

type TextTransQueryRespData struct {
	ErrorCode   int    `json:"error_code"`
	TaskID      string `json:"task_id"`
	Status      string `json:"status"`
	Translation string `json:"translation"`
}

type TextTransCallReq struct {
	Text     string `json:"text"`
	TaskID   string `json:"task_id"`
	FromLang string `json:"from_lang"`
	ToLang   string `json:"to_lang"`
}

func (w *TextTranslateWorker) Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Starting text translate task: %d, TextTranslateWorker:%v", t.Id, w)

	// 1. Get task request
	createReq := &TextTransCallReq{}
	if err := json.Unmarshal([]byte(t.CommonInputContent), createReq); err != nil {
		g.Log().Errorf(ctx, "Failed to unmarshal task request: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to unmarshal task request: %v", err),
		}
	}
	g.Log().Debugf(ctx, "Parsed task request: %+v", createReq)
	createReq.TaskID = fmt.Sprintf("%d", t.Id)
	createResp := &TextTransQueryResp{}
	if err := w.PostJSON(ctx, w.createUrl, createReq, createResp); err != nil {
		g.Log().Errorf(ctx, "Failed to create text translate task: %v", err)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to create text translate task: %v", err),
		}
	}
	if createResp == nil || createResp.Code != 0 {
		g.Log().Errorf(ctx, "Failed to create text translate task: %v", createResp)
		return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
			Code:    CodeTaskFailed,
			Message: fmt.Sprintf("Failed to create text translate task: %v", createResp),
		}
	}

	return w.queryTaskResult(ctx, createReq.TaskID)
}

func (w *TextTranslateWorker) queryTaskResult(ctx context.Context, taskId string) (omniEngine.TaskStatus, *TaskOutputContent) {
	g.Log().Infof(ctx, "Querying text translate task result: %s", taskId)
	resultUrl := w.resultUrl + "/" + taskId
	timeout := time.NewTimer(time.Hour)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			g.Log().Errorf(ctx, "Timeout waiting for text translate task %s to complete", taskId)
			return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
				Code:    CodeTaskFailed,
				Message: fmt.Sprintf("Timeout waiting for text translate task %s to complete", taskId),
			}
		default:
			resultResp := &TextTransQueryResp{}
			err := w.GetJSON(ctx, resultUrl, resultResp)
			if err != nil {
				g.Log().Errorf(ctx, "Failed to get text translate task result: %v", err)
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    CodeTaskFailed,
					Message: fmt.Sprintf("Failed to get text translate task result: %v", err),
				}
			}
			if resultResp.Code != 0 {
				return omniEngine.TaskStatus_FAILED, &TaskOutputContent{
					Code:    resultResp.Code,
					Message: resultResp.Message,
				}
			}
			// 执行成功
			if resultResp.Code == 0 && resultResp.Data.ErrorCode == 0 && resultResp.Data.Translation != "" && resultResp.Data.Status == "completed" {
				g.Log().Infof(ctx, "Text translate task completed: %s", taskId)
				return omniEngine.TaskStatus_COMPLETED, &TaskOutputContent{
					Code:    CodeCompleted,
					Message: StatusCompleted,
					Data:    resultResp,
				}
			}
			time.Sleep(time.Duration(w.checkResultInterval) * time.Millisecond)
		}
	}
}

func (w *TextTranslateWorker) GetWorkerName() string {
	return config.GetTaskNameByType(w.taskType)
}

func (w *TextTranslateWorker) Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent) {
	return omniEngine.TaskStatus_FAILED, nil
}
