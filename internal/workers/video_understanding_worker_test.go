package workers

import (
	"context"
	"testing"

	"omni_worker/pkg/config"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

func TestVideoUnderstandingWorker(t *testing.T) {
	PatchConvey("TestVideoUnderstandingWorker", t, func() {
		Mock(config.GetConfig).Return(&config.Config{
			VideoUnderstandingConfig: config.VideoUnderstandingConfig{
				WorkerConfig: config.WorkerConfig{
					TimeoutSeconds:       600,
					SubmitTaskIntervalMs: 1000,
				},
				CreateUrl: "https://testing.all-voice.cn/explain-video/api/understanding",
			},
		}).Build()
		worker := &VideoUnderstandingWorker{
			BaseWorker: NewBaseWorker(),
			taskType:   omniEngine.TaskType_VIDEO_UNDERSTANDING,
		}
		status, output := worker.handleVideoUnderstandingTask(context.Background(), &VideoUnderstandingReq{
			TaskID:   123,
			VideoURL: "https://cdn-allvoice-down-cn-testing.funnycp.com/allvoice/test/private/video_trans/2597313014839392802/20250920/d7080c70-0539-47e4-a916-710f25ca42be.mp4",
		})
		So(status, ShouldEqual, omniEngine.TaskStatus_COMPLETED)
		So(output, ShouldNotBeNil)
	})
}
