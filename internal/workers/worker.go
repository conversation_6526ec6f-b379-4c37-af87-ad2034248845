package workers

import (
	"context"
	"encoding/json"
	"fmt"

	"omni_worker/internal/consts"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

// Worker 接口
type Worker interface {
	Process(ctx context.Context, t *omniEngine.Task) (omniEngine.TaskStatus, *TaskOutputContent)
	Callback(ctx context.Context, t *omniEngine.Task, input any) (omniEngine.TaskStatus, *TaskOutputContent)
	GetWorkerName() string
}

type BaseWorker struct {
	restClient *trace.XHttpClient
}

type TaskOutputContent struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func NewBaseWorker() *BaseWorker {
	return &BaseWorker{
		restClient: trace.NewXHttpClient(),
	}
}

func (w *BaseWorker) PostJSON(ctx context.Context, url string, requestBody interface{}, responseBody interface{}) error {
	resp, err := w.restClient.R(ctx).
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader(consts.TaskContextKeyCluster, GetClusterFromContext(ctx)). // 添加集群信息，用于算法判断使用oss client
		SetBody(requestBody).
		SetResult(responseBody).
		Post(url)
	if err != nil {
		g.Log().Errorf(ctx, "HTTP request failed: %v", err)
		return err
	}
	bs, _ := json.Marshal(requestBody)
	g.Log().Infof(ctx, "postJSON url: %s, HTTP response raw body: %s, req: %s", url, resp.String(), string(bs))
	if resp.StatusCode() != 200 {
		g.Log().Errorf(ctx, "postJSON url: %s,HTTP request failed with status code: %d, response raw body: %s,req: %s", url, resp.StatusCode(), resp.String(), string(bs))
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode())
	}

	return nil
}

func (w *BaseWorker) GetJSON(ctx context.Context, url string, responseBody interface{}) error {
	resp, err := w.restClient.R(ctx).
		SetContext(ctx).
		SetResult(responseBody).
		Get(url)
	if err != nil {
		g.Log().Errorf(ctx, "HTTP request failed: %v", err)
		return err
	}

	g.Log().Infof(ctx, "HTTP response raw body: %s", resp.String())
	if resp.StatusCode() != 200 {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode())
	}

	return nil
}

func GetClusterFromContext(ctx context.Context) string {
	cluster, ok := ctx.Value(consts.TaskContextKeyCluster).(string)
	if !ok {
		return ""
	}
	return cluster
}

func (w *BaseWorker) postJsonWithHeader(
	ctx context.Context,
	url string,
	requestBody interface{},
	responseBody interface{},
	requestHeaders map[string]string,
	responseHeader map[string]string,
) error { // 返回响应头信息
	req := w.restClient.R(ctx).
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader(consts.TaskContextKeyCluster, GetClusterFromContext(ctx)).
		SetBody(requestBody).
		SetResult(responseBody)

	// 添加调用方传入的自定义 header
	for k, v := range requestHeaders {
		req.SetHeader(k, v)
	}

	resp, err := req.Post(url)
	if err != nil {
		g.Log().Errorf(ctx, "HTTP request failed: %v", err)
		return err
	}

	g.Log().Infof(ctx, "HTTP response url:%s raw body: %s", url, resp.String())

	if resp.StatusCode() != 200 {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode())
	}
	if responseHeader != nil {
		for k, v := range resp.Header() {
			responseHeader[k] = v[0]
		}
	}
	return nil
}

// func ResetClusterFromContext(ctx context.Context) context.Context {
// 	cluster, ok := ctx.Value(consts.TaskContextKeyCluster).(string)
// 	if !ok {
// 		return ctx
// 	}
// 	cfg := config.GetConfig()
// 	ctx = context.WithValue(ctx, consts.TaskContextKeyCluster, fmt.Sprintf("%s-%s", cfg.Server.Env, cluster))
// 	return ctx
// }

//func (w *BaseWorker) sendAlert(ctx context.Context, taskID int64, taskType, alertInfo string) {
//	concurrent.GoSafe(func() {
//		var reqId, trafficMark string
//		// 从上下文获取ReqId和TrafficMark
//		if reqIdVal, ok := ctx.Value(trace.ReqId).(string); ok {
//			reqId = reqIdVal
//		}
//		if trafficMarkVal, ok := ctx.Value(trace.TrafficMark).(string); ok {
//			trafficMark = trafficMarkVal
//		}
//		alertDetails := notifier.AlertDetails{
//			TaskID:      taskID,
//			ReqId:       reqId,
//			TrafficMark: trafficMark,
//			TaskType:    taskType,
//			AlertInfo:   alertInfo,
//		}
//
//		alertMessage := notifier.NewAlertNotification("P1", "任务失败", alertDetails)
//		notifier.SendNotification(ctx, alertMessage)
//	})
//}
//
//func (w *BaseWorker) GetGrpcClient(ctx context.Context) (*client.GRPCClient, error) {
//
//	cfg := config.GetConfig()
//	grpcClient, err := client.NewGRPCClient(ctx, cfg.OmniEngine.GRPCAddress)
//	if err != nil {
//		g.Log().Errorf(ctx, "failed to create write grpc client: %v", err)
//		return nil, err
//	}
//	return grpcClient, nil
//}
