package client

import (
	"context"
	"errors"
	"fmt"
	"omni_worker/pkg/config"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"google.golang.org/grpc/credentials/insecure"

	"google.golang.org/grpc"
)

var (
	grpcManagerObj *GRPCManager
	mu             sync.RWMutex
)

func newGRPCConn(ctx context.Context, grpcAddress string) *grpc.ClientConn {
	conn, err := grpc.NewClient(grpcAddress, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithUnaryInterceptor(trace.RequestInterceptorForClient()),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(100*1024*1024), grpc.MaxCallRecvMsgSize(100*1024*1024)))
	if err != nil {
		panic(errors.New(fmt.Sprintf("Failed to connect to gRPC server: %v", err)))
	}
	return conn
}

type GRPCManager struct {
	omniEngineGRPCClientMap map[string]*OmniEngineClient
}

func (m *GRPCManager) GetOmniEngineClient(ctx context.Context, cluster string) *OmniEngineClient {
	client, ok := m.omniEngineGRPCClientMap[cluster]
	if !ok {
		g.Log().Errorf(ctx, "omni engine client not found for cluster %s", cluster)
		return nil
	}
	return client
}

func (m *GRPCManager) close(ctx context.Context) {
	for _, client := range m.omniEngineGRPCClientMap {
		client.close()
	}
}

type OmniEngineClient struct {
	conn   *grpc.ClientConn
	client omniEngine.OmniEngineServiceClient
}

func newOmniEngineClient(ctx context.Context, grpcAddress string) *OmniEngineClient {
	conn := newGRPCConn(ctx, grpcAddress)
	return &OmniEngineClient{
		conn:   conn,
		client: omniEngine.NewOmniEngineServiceClient(conn),
	}
}

func (c *OmniEngineClient) close() error {
	return c.conn.Close()
}

func InitGRPCClient(ctx context.Context, cfg *config.Config) {
	mu.Lock()
	defer mu.Unlock()
	tmp := &GRPCManager{}
	// 初始化omni_engine连接
	tmp.omniEngineGRPCClientMap = make(map[string]*OmniEngineClient)
	omniEngineGRPCConfig := cfg.OmniEngineGRPCConfig
	// 如果任务消费配置关闭，则只初始化默认集群的grpc client
	if !omniEngineGRPCConfig.TaskConsumeConfig.Enable {
		defaultAddress := omniEngineGRPCConfig.EndPoints[omniEngineGRPCConfig.DefaultCluster]
		tmp.omniEngineGRPCClientMap[cfg.OmniEngineGRPCConfig.DefaultCluster] = newOmniEngineClient(ctx, defaultAddress)
	} else {
		for cluster, address := range omniEngineGRPCConfig.EndPoints {
			tmp.omniEngineGRPCClientMap[cluster] = newOmniEngineClient(ctx, address)
		}
	}
	// 关闭旧的grpc conn
	if grpcManagerObj != nil {
		grpcManagerObj.close(ctx)
	}
	grpcManagerObj = tmp
}

func GetGRPCMgr() *GRPCManager {
	mu.RLock()
	defer mu.RUnlock()
	return grpcManagerObj
}
