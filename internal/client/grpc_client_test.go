package client

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
)

func Test_GetTask(t *testing.T) {
	ctx := context.Background()
	c, err := NewGRPCClient(ctx, "localhost:50051")
	assert.Nil(t, err)
	task, err := c.GetTask(ctx, omniEngine.TaskType_TTS_SERVICE_MULTILANG)
	assert.<PERSON>l(t, err)
	assert.NotNil(t, task)
}
