package client

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	omniEngine "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"google.golang.org/grpc"
)

var lastNotFoundLogTick = time.Now()

func (c *OmniEngineClient) GetTask(ctx context.Context, taskType omniEngine.TaskType) (*omniEngine.Task, error) {
	req := &omniEngine.GetTaskFromQueueRequest{
		Type:      taskType,
		UniqueKey: generateIdempotencyKey(taskType),
	}
	resp, err := c.client.GetTaskFromQueue(ctx, req, grpc.MaxCallRecvMsgSize(100*1024*1024))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to fetch %s task: %v", taskType.String(), err)
		return nil, err
	}

	switch resp.Code {
	case omniEngine.StatusCode_OK:
		return resp.Task, nil
	case omniEngine.StatusCode_TASK_NOT_FOUND:
		if time.Since(lastNotFoundLogTick) > 30*time.Second {
			lastNotFoundLogTick = time.Now()
			g.Log().Infof(ctx, "No %s tasks available in queue", taskType.String())
		}
		return nil, fmt.Errorf("no %s tasks available in queue", taskType.String())
	default:
		g.Log().Errorf(ctx, "Task fetch failed, error code: %d, error message: %s", resp.Code, resp.Msg)
		return nil, fmt.Errorf("task fetch failed, error code: %d, error message: %s", resp.Code, resp.Msg)
	}
}

func (c *OmniEngineClient) UpdateTask(ctx context.Context, taskId int64, status omniEngine.TaskStatus, taskResult string, workerStartAt time.Time, workerEndAt time.Time) error {
	req := &omniEngine.UpdateTaskRequest{
		TaskId:            taskId,
		Status:            status,
		TaskOutputContent: taskResult,
		WorkerStartAtMs:   workerStartAt.UnixMilli(),
		WorkerEndAtMs:     workerEndAt.UnixMilli(),
	}

	g.Log().Infof(ctx, "UpdateTask Request: %+v", req)
	resp, err := c.client.UpdateTask(ctx, req, grpc.MaxCallSendMsgSize(100*1024*1024))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to update task status: %v", err)
		return err
	}

	g.Log().Infof(ctx, "UpdateTask Response: %+v", resp)
	if resp.Code != omniEngine.StatusCode_OK {
		g.Log().Errorf(ctx, "Task update failed, error code: %d, error message: %s", resp.Code, resp.Msg)
		return err
	}

	return nil
}

func (c *OmniEngineClient) ReportTasksStatus(ctx context.Context, taskList []*omniEngine.TaskStatusReport) error {
	req := &omniEngine.ReportTasksStatusRequest{
		Tasks: taskList,
	}

	g.Log().Infof(ctx, "ReportTasksStatus Request: %+v", req)
	resp, err := c.client.ReportTasksStatus(ctx, req)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to report task status: %v", err)
		return err
	}

	g.Log().Infof(ctx, "ReportTasksStatus Response: %+v", resp)
	if resp.Code != omniEngine.StatusCode_OK {
		g.Log().Errorf(ctx, "Task status report failed, error code: %d, error message: %s", resp.Code, resp.Msg)
		return err
	}

	return nil
}

func (c *OmniEngineClient) ReportAlgorithmVersion(ctx context.Context, taskType omniEngine.TaskType, version string) error {
	req := &omniEngine.ReportAlgorithmVersionReq{
		TaskType: taskType,
		Version:  version,
	}
	g.Log().Infof(ctx, "ReportAlgorithmVersion Request: %+v", req)
	_, err := c.client.ReportAlgorithmVersion(ctx, req)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to report algorithm version: %v", err)
		return err
	}
	g.Log().Infof(ctx, "ReportAlgorithmVersion is success")
	return nil
}

func generateIdempotencyKey(taskType omniEngine.TaskType) string {
	return fmt.Sprintf("%s-%d", taskType.String(), time.Now().UnixNano())
}
