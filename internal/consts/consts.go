package consts

import "github.com/gogf/gf/v2/os/gctx"

type ContextKey string

type TaskTypeEnum string

const (
	TaskId = "task-id"

	ConfigFile        = "CONFIG_FILE"
	LaunchTaskType    = "LAUNCH_TASK_TYPE"
	HealthCheckSwitch = "HEALTH_CHECK_SWITCH"

	TaskTypeEnumOCR                             TaskTypeEnum = "OCR"                                 // OCR识别
	TaskTypeEnumTTSMultiLang                    TaskTypeEnum = "TTS_MULTILANG"                       // TTS多语言
	TaskTypeEnumTTSCanton                       TaskTypeEnum = "TTS_CANTON"                          // TTS粤语
	TaskTypeEnumTTSVi                           TaskTypeEnum = "TTS_VI"                              // TTS越南语
	TaskTypeEnumTTSPvc                          TaskTypeEnum = "TTS_PVC"                             // TTS专业音频
	TaskTypeEnumMinimaxTTS                      TaskTypeEnum = "MINIMAX_TTS"                         // minimax TTS
	TaskTypeEnum11LabsTTS                       TaskTypeEnum = "11LABS_TTS"                          // 11labs TTS
	TaskTypeSubtitleRemoval                     TaskTypeEnum = "SUBTITLE_REMOVAL"                    // 字幕擦除
	TaskTypeEnumAudioSeparate                   TaskTypeEnum = "AUDIO_SEPARATE"                      // 人声分离
	TaskTypeEnumASR                             TaskTypeEnum = "ASR"                                 // 音频识别文字
	TaskTypeEnumSTS                             TaskTypeEnum = "STS"                                 // 音色转换
	TaskTypeEnumVoiceIsolate                    TaskTypeEnum = "VOICE_ISOLATE"                       // 噪音分离
	TaskTypeEnumVideoTranslateErase             TaskTypeEnum = "VIDEO_TRANSLATE_ERASE"               // 视频翻译擦除
	TaskTypeEnumVideoTranslateExtract           TaskTypeEnum = "VIDEO_TRANSLATE_EXTRACT"             // 视频翻译提取
	TaskTypeEnumVideoTranslatePreprocess        TaskTypeEnum = "VIDEO_TRANSLATE_PREPROCESS"          // 视频翻译预处理
	TaskTypeEnumVideoTranslatePostMerge         TaskTypeEnum = "VIDEO_TRANSLATE_POST_MERGE"          // 视频翻译后处理
	TaskTypeEnumVideoTranslateV2PreProcess      TaskTypeEnum = "VIDEO_TRANSLATE_V2_PREPROCESS"       // 视频翻译V2预处理
	TaskTypeEnumVideoTranslateV2AsrCorrect      TaskTypeEnum = "VIDEO_TRANSLATE_V2_ASR_CORRECT"      // 视频翻译V2ASR纠错
	TaskTypeEnumVideoTranslateV2TtsCorrect      TaskTypeEnum = "VIDEO_TRANSLATE_V2_TTS_CORRECT"      // 视频翻译V2TTS纠错
	TaskTypeEnumVideoTranslateV2Tts             TaskTypeEnum = "VIDEO_TRANSLATE_V2_TTS"              // 视频翻译V2TTS
	TaskTypeEnumTextTranslate                   TaskTypeEnum = "TEXT_TRANSLATE"                      // 文本翻译
	TaskTypeEnumSrtTextTranslate                TaskTypeEnum = "SRT_TRANSLATE"                       // SRT翻译
	TaskTypeEnumAudioLangDetect                 TaskTypeEnum = "AUDIO_LANG_DETECT"                   // 音频语言识别
	TaskTypeEnumAudioVolcengineAsr              TaskTypeEnum = "AUDIO_VOLCENGINE_ASR"                // Volcengine ASR音频识别
	TaskTypeEnumAgentTranslate                  TaskTypeEnum = "AGENT_TRANSLATE"                     // 代理翻译
	TaskTypeEnumVideoTranslateErasePro          TaskTypeEnum = "VIDEO_TRANSLATE_ERASE_PRO"           // 视频翻译专业擦除
	TaskTypeEnumVideoTranslatePreprocessPro     TaskTypeEnum = "VIDEO_TRANSLATE_PREPROCESS_PRO"      // 视频翻译专业预处理
	TaskTypeEnumVideoTranslateSubtitleMerge     TaskTypeEnum = "VIDEO_TRANSLATE_SUBTITLE_MERGE"      // 视频翻译视频合成
	TaskTypeEnumVideoTranslateSubtitleMergeLong TaskTypeEnum = "VIDEO_TRANSLATE_SUBTITLE_MERGE_LONG" // 视频翻译长视频合成
	TaskTypeEnumVideoTranscoding                TaskTypeEnum = "VIDEO_TRANSCODING"                   // 视频转码
	TaskTypeEnumVideoCommentaryQiFei            TaskTypeEnum = "VIDEO_COMMENTARY_QIFEI"              // 起飞解说
	TaskTypeEnumCommentarySubtitleMerge         TaskTypeEnum = "COMMENTARY_SUBTITLE_MERGE"           // 解说字幕合成
	TaskTypeEnumVideoUnderstanding              TaskTypeEnum = "VIDEO_UNDERSTANDING"                 // 视频理解
	TaskTypeEnumVideoSubtitleGeneration         TaskTypeEnum = "VIDEO_SUBTITLE_GENERATION"           // 视频字幕生成
	TaskTypeEnumVideoAlignment                  TaskTypeEnum = "VIDEO_ALIGNMENT"                     // 视频对齐
	TaskTypeEnumVideoHighlightClipping          TaskTypeEnum = "VIDEO_HIGHLIGHT_CLIPPING"            // 视频高亮剪辑
)

const (
	ContextKeyEngineTaskId gctx.StrKey = "engineTaskId"
)

const (
	TaskContextKeyCluster string = "cluster"
)

const (
	EraseModelLite = "lite"
	EraseModelPro  = "pro"
)

const (
	CommonCodeOk   = 0 // 成功
	CommonCodeFail = 1 // 错误
)
