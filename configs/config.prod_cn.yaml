server:
  http_address: ":29000"
  env: "prod"

omni_engine:
  grpc_address: "omni-engine-cn.basic-service-cn.svc.cluster.local:50051"

logger:
  level: "all"
  stdout: true

tts_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  check_watermark_url: "http://127.0.0.1:6030/voiceclone/v1/check_watermark"
  create_tts_url: "http://127.0.0.1:6030/voiceclone/v1/tts"
  check_status_url: "http://127.0.0.1:6030/voiceclone/v1/check_status"

audio_separate_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  create_url: "http://127.0.0.1:6030/preproc/v1/submit_preproc"
  result_url: "http://127.0.0.1:6030/preproc/v1/get_preproc_result"

voice_isolate_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  create_url: "http://127.0.0.1:6033/preproc/v1/submit_voice_isolate"
  result_url: "http://127.0.0.1:6033/preproc/v1/get_voice_isolate_result"

asr_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  create_url: "http://127.0.0.1:6030/asr/v1/run_asr"

sts_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  create_url: "http://127.0.0.1:6030/voiceclone/v1/voice_conversion"
  result_url: "http://127.0.0.1:6030/voiceclone/v1/check_status"

pvc_tts_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  create_url: "http://127.0.0.1:5000/inference"
  result_url: "http://127.0.0.1:5000/fetch_result"

video_translate_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  extract_v2_create_url: "http://127.0.0.1:35000/v2/ocr_det_extract"
  extract_v2_query_url: "http://127.0.0.1:35000/v2/get_extract_status"
  erase_v2_create_url: "http://127.0.0.1:35005/v2/video_inpaint"
  erase_v2_query_url: "http://127.0.0.1:35005/v2/get_video_inpaint_status"
  merge_v2_create_url: "http://127.0.0.1:35005/v2/postprocess"
  merge_v2_query_url: "http://127.0.0.1:35005/v2/get_postprocess_status"
  preprocess_v2_create_url: "http://127.0.0.1:35005/v2/preprocess"
  preprocess_v2_query_url: "http://127.0.0.1:35005/v2/get_preprocess_status"
  audio_preprocess_create_url: "http://127.0.0.1:6030/preproc/v1/submit_preproc"
  tts_correction_create_url: "http://127.0.0.1:6030/voiceclone/v1/fix_audio_segmentation"

text_translate_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  create_url: "http://127.0.0.1:35001/translation/text/file_translate"
  result_url: "http://127.0.0.1:35001/translation/text/file_translate"

srt_translate_service:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 1  # 最大同时处理的任务数
  srt_translate_url: "http://127.0.0.1:35001/translation/text/translate_json"

eleven_labs_config:
  interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
  report_seconds: 3  # 定时上报任务的时间间隔（秒）
  max_concurrent_tasks: 15  # 最大同时处理的任务数
  api_key: "***************************************************"
  create_tts_url: "https://api.elevenlabs.io/v1/text-to-speech/%s"
  create_voice_url: "https://api.elevenlabs.io/v1/voices/add"
  delete_voice_url: "https://api.elevenlabs.io/v1/voices/%s"
  default_model_id: "eleven_multilingual_v2"
  model_id_map:
    hu: "eleven_turbo_v2_5"
    no: "eleven_turbo_v2_5"

notifier:
  feishu_token: "3a2bd74a-3b92-4277-93ea-e6f411f82903"
  feishu_secret: "48i46pNOjFO6ueV5bYl3Wg"

task_monitor:
  task_type_map:
    20: "TTS多语言_20"
    21: "TTS粤语_21"
    22: "TTS越南语_22"
    23: "TTS_PVC_23"
    27: "TTS_MINIMAX_27"
    40: "音频分离_40"
    50: "ASR_50"
    60: "音色转换_60"
    70: "噪音分离_70"
    80: "视频翻译_80"
    81: "视频翻译擦除_81"
    82: "视频翻译OCR提取_82"
    83: "视频翻译预处理_83"
    84: "视频翻译合成_84"
    85: "视频翻译v2预处理_85"
    86: "视频翻译V2ASR校正_86"
    87: "视频翻译V2TTS校正_87"
    88: "视频翻译V2TTS_88"
    90: "视频翻译配音_90"
    91: "视频翻译提取_91"
    92: "视频翻译擦除提取_92"
    100: "全文本翻译_100"
    101: "SRT文本翻译_101"

health_check_config:
  health_check_interval: 10
  task_health_url_map:
    20: "http://127.0.0.1:6030/health"
    21: "http://127.0.0.1:6030/health"
    22: "http://127.0.0.1:6030/health"
    23: "http://127.0.0.1:5000/health"
    27: ""
    40: "http://127.0.0.1:6030/health"
    50: "http://127.0.0.1:6030/health"
    60: "http://127.0.0.1:6030/health"
    70: "http://127.0.0.1:6033/health"
    80: ""
    81: "http://127.0.0.1:35005/health"
    82: "http://127.0.0.1:35000/health"
    83: "http://127.0.0.1:35005/health"
    84: "http://127.0.0.1:35005/health"
    85: ""
    86: "http://127.0.0.1:35001/health"
    87: "http://127.0.0.1:6030/health"
    88: "http://127.0.0.1:35005/health"
    90: ""
    91: ""
    92: ""

obs:
  vendor: ali
  access_key: LTAI5tKH4Xa6QL8WZv4w1XZa
  secret_key: ******************************
  endpoint: oss-cn-beijing-internal.aliyuncs.com
  bucket: oss-prod-ali-bj-allvoice-cn
  cdn: cdn.allvoicelab.cn
  object_dir: openapi

third_party_tts_service:
  default_worker_config:
    interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
    report_seconds: 3  # 定时上报任务的时间间隔（秒）
    max_concurrent_tasks: 20  # 最大同时处理的任务数

  # TTS配置映射
  tts_config_map:
    # Minimax TTS配置
    "27":
      worker_config:
        interval_milliseconds: 500  # 定时调度任务的时间间隔（秒）
        report_seconds: 3  # 定时上报任务的时间间隔（秒）
        max_concurrent_tasks: 20 # 最大同时处理的任务数
      url: "https://aigc-backend.skyengine.com.cn/eliza/audio/speech"
      authKey: "xiangmu157:feeb1d13b5f8679d5a1c9041728c5bbe"
      model: "speech-01-hd"
      format: "mp3"
      retry_config:
        retry_interval: 5      # 重试间隔（秒）
        max_retry_count: 10     # 最大重试次数
        base_delay: 2          # 基础延迟（秒）
        max_delay: 10          # 最大延迟（秒）
        backoff_factor: 2.0    # 退避因子
      modelExtra:
        audio_sample_rate: 24000
        bitrate: 256000