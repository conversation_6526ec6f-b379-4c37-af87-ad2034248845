const commonFonts = [
  { label: "HarmonyOS Sans", value: "HarmonyOS_Sans" },
  { label: "阿里巴巴普惠体", value: "Alibaba_PuHuiTi" },
  { label: "MiSans", value: "MiSans" }
]
const customFonts = {
  zh: [
    { label: "思源宋体", value: "SourceHanSerifCN" },
    { label: "思源黑体", value: "SourceHanSansCN" }
  ],
  en: [
    { label: "Georgia", value: "Georgia" },
    { label: "Verdana", value: "Verdana" },
    { label: "<PERSON><PERSON>ri", value: "<PERSON><PERSON>ri" }
  ],
  ru: [
    { label: "Georgia", value: "Georgia" },
    { label: "Verdana", value: "Verdana" },
    { label: "<PERSON><PERSON><PERSON>", value: "<PERSON><PERSON>ri" },
    { label: "Roboto", value: "Roboto" }
  ]
}

// 以下语言有固定字体，不展示共用字体
const customLang = {
  // 越南语
  vi: [{ label: "Alibaba Sans Viet", value: "Alibaba Sans Viet" }],
  // 韩语
  ko: [{ label: "Alibaba Sans KR", value: "Alibaba Sans KR" }],
  // 日语
  ja: [{ label: "Alibaba Sans JP", value: "Alibaba Sans JP" }],
  // 泰语
  th: [{ label: "Alibaba Sans Thai", value: "AlibabaSansThai-Rg" }],
  // // 印地语
  // hi: [{ label: "MiSans Devanagari", value: "MiSansDevanagari-Normal" }],
  // 高棉语
  km: [{ label: "MiSans Khmer", value: "MiSansKhmer-Normal" }],
  // 缅甸语
  my: [{ label: "MiSans Myanmar", value: "MiSansMyanmar-Normal" }],
  //泰米尔语
  ta: [{ label: "Karla Tamil Upright", value: "Karla Tamil Upright" }],
  //阿拉伯语
  ar: [{ label: "Noto Naskh Arabic", value: "NotoNaskhArabic-Rg" }],
  // 挪威语
  no: {
    "NotoSans-Regular": "https://cdn.allvoicelab.cn/resources/font/NotoSans-Regular.ttf",
    "NotoSans-Regular_Bold": "https://cdn.allvoicelab.cn/resources/font/NotoSans-Bold.ttf"
  },
// 罗马尼亚语
  ro: {
    "NotoSans-Regular": "https://cdn.allvoicelab.cn/resources/font/NotoSans-Regular.ttf",
    "NotoSans-Regular_Bold": "https://cdn.allvoicelab.cn/resources/font/NotoSans-Bold.ttf"
  },
// 印地语
  hi: {
    "NotoSansDevanagari-Regular": "https://cdn.allvoicelab.cn/resources/font/NotoSansDevanagari-Regular.ttf",
    "NotoSansDevanagari-Regular_Bold": "https://cdn.allvoicelab.cn/resources/font/NotoSansDevanagari-Bold.ttf"
  },
}
export const langFonts = {
  SourceHanSerifCN: { url: "https://cdn.allvoicelab.cn/resources/font/SourceHanSerifCN.ttf", name: "source han serif cn vf" },
  "SourceHanSerifCN_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/SourceHanSerifCN-Bold.ttf", name: "source han serif cn vf" },
  SourceHanSansCN: { url: "https://cdn.allvoicelab.cn/resources/font/SourceHanSansCN.ttf", name: "source han sans cn vf" },
  "SourceHanSansCN_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/SourceHanSansSC-VF.ttf", name: "source han sans cn vf" },
  Georgia: { url: "https://cdn.allvoicelab.cn/resources/font/Georgia.ttf", name: "georgia" },
  Georgia_Bold: { url: "https://cdn.allvoicelab.cn/resources/font/Georgia-Bold.ttf", name: "georgia" },
  Verdana: { url: "https://cdn.allvoicelab.cn/resources/font/Verdana.ttf", name: "verdana" },
  Verdana_Bold: { url: "https://cdn.allvoicelab.cn/resources/font/Verdana-Bold.ttf", name: "verdana" },
  Calibri: { url: "https://cdn.allvoicelab.cn/resources/font/Calibri.ttf", name: "calibri" },
  Calibri_Bold: { url: "https://cdn.allvoicelab.cn/resources/font/Calibri-Bold.ttf", name: "calibri" },
  Roboto: { url: "https://cdn.allvoicelab.cn/resources/font/Roboto.ttf", name: "roboto" },
  "Roboto_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/Roboto-Bold.ttf", name: "roboto" },
  "HarmonyOS_Sans": { url: "https://cdn.allvoicelab.cn/resources/font/HarmonyOS_Sans_SC_Regular.ttf", name: "harmonyos sans sc" },
  "HarmonyOS_Sans_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/HarmonyOS_Sans_SC_Medium.ttf", name: "harmonyos sans sc medium" },
  "Alibaba_PuHuiTi": { url: "https://cdn.allvoicelab.cn/resources/font/Alibaba_PuHuiTi.ttf", name: "alibaba puhuiti 3.0 55 regular" },
  "Alibaba_PuHuiTi_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaPuHuiTi-3-75-SemiBold.ttf", name: "alibaba puhuiti 3.0 75 semibold" },
  "MiSans": { url: "https://cdn.allvoicelab.cn/resources/font/MiSans.ttf", name: "misans" },
  "MiSans_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/MiSans-Bold.ttf", name: "misans" },
  "Alibaba Sans Viet": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansViet-Rg.ttf", name: "alibaba sans viet" },
  "Alibaba Sans Viet_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansViet-Bd.ttf", name: "alibaba sans viet" },
  "Alibaba Sans KR": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansKR-Regular.ttf", name: "alibaba sans kr" },
  "Alibaba Sans KR_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansKR-Bold.ttf", name: "alibaba sans kr" },
  "Alibaba Sans JP": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansJP-Regular.ttf", name: "alibaba sans jp" },
  "Alibaba Sans JP_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansJP-Bold.ttf", name: "alibaba sans jp" },
  "AlibabaSansThai-Rg": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansThai-Rg.ttf", name: "alibaba sans thai", "" },
  "AlibabaSansThai-Rg_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/AlibabaSansThai-Bd.ttf", name: "alibaba sans thai" },
  "MiSansDevanagari-Normal": { url: "https://cdn.allvoicelab.cn/resources/font/MiSansDevanagari-Normal.ttf", name: "misans devanagari" },
  "MiSansDevanagari-Normal_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/MiSansDevanagari-Bold.ttf", name: "misans devanagari" },
  "MiSansKhmer-Normal": { url: "https://cdn.allvoicelab.cn/resources/font/MiSansKhmer-Normal.ttf", name: "misans khmer" },
  "MiSansKhmer-Normal_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/MiSansKhmer-Bold.ttf", name: "misans khmer" },
  "MiSansMyanmar-Normal": { url: "https://cdn.allvoicelab.cn/resources/font/MiSansMyanmar-Normal.ttf", name: "misans myanmar" },
  "MiSansMyanmar-Normal_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/MiSansMyanmar-Bold.ttf", name: "misans myanmar" },
  "Karla Tamil Upright": { url: "https://cdn.allvoicelab.cn/resources/font/KarlaTamilUpright-Regular.ttf", name: "karla tamil upright" },
  "karla tamil upright_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/KarlaTamilUpright-Bold.ttf", name: "Karla Tamil Upright" },
  "NotoNaskhArabic-Rg": { url: "https://cdn.allvoicelab.cn/resources/font/NotoNaskhArabic-Regular.ttf", name: "noto naskh arabic" },
  "NotoNaskhArabic-Rg_Bold": { url: "https://cdn.allvoicelab.cn/resources/font/NotoNaskhArabic-Bold.ttf", name: "noto naskh arabic" },
}

export { commonFonts, customFonts, customLang }